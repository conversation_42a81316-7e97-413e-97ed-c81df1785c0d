<?php

namespace App\Http\Resources\AI;

use App\Enums\AI\MessageRole;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $messageRole = MessageRole::tryFrom($this->role);

        return [
            'uuid' => $this->uuid,
            'role' => $this->role,
            'role_name' => $messageRole ? $messageRole->getName() : $this->role,
            'role_color' => $messageRole ? $messageRole->getColor() : 'gray',
            'content' => $this->content,
            'content_type' => $this->content_type,
            'metadata' => $this->metadata,
            'attachments' => $this->attachments,
            'provider_used' => $this->provider_used,
            'tokens_used' => $this->tokens_used,
            'cost' => $this->cost,
            'is_error' => $this->is_error,
            'error_message' => $this->error_message,
            'user' => $this->when($this->user, function () {
                return [
                    'uuid' => $this->user->uuid,
                    'name' => $this->user->name,
                ];
            }),
            'conversation' => $this->when($this->conversation && $request->route()->getName() !== 'ai.conversations.messages.index', function () {
                return [
                    'uuid' => $this->conversation->uuid,
                    'title' => $this->conversation->title,
                    'type' => $this->conversation->type,
                ];
            }),
            'created_at' => \Cal::dateTime($this->created_at),
            'updated_at' => \Cal::dateTime($this->updated_at),
        ];
    }
}
