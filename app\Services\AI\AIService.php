<?php

namespace App\Services\AI;

use App\Models\AI\Provider;
use App\Models\AI\Conversation;
use App\Models\AI\Message;
use App\Models\AI\UsageLog;
use App\Services\AI\Providers\OpenAIProvider;
use App\Services\AI\Providers\GeminiProvider;
use App\Services\AI\Providers\DeepSeekProvider;
use App\Services\AI\Providers\AnthropicProvider;
use App\Services\AI\Providers\XAIProvider;
use App\Services\AI\GlobalProviderService;
use Illuminate\Support\Facades\Log;

class AIService
{
    private array $providers = [];

    public function __construct()
    {
        $this->providers = [
            'openai' => OpenAIProvider::class,
            'gemini' => GeminiProvider::class,
            'deepseek' => DeepSeekProvider::class,
            'anthropic' => AnthropicProvider::class,
            'xai' => XAIProvider::class,
        ];
    }

    public function sendMessage(
        string $message,
        Conversation $conversation,
        ?Provider $preferredProvider = null
    ): Message {
        $startTime = microtime(true);
        $provider = $preferredProvider ?? $this->getAvailableProvider($conversation);

        if (!$provider) {
            throw new \Exception('No AI provider available');
        }

        try {
            // Create user message (only if conversation saving is enabled, check global config first)
            $userMessage = null;
            $saveConversations = GlobalProviderService::getConfigValue('save_conversations', true);
            if ($saveConversations && $conversation->exists) {
                $userMessage = $this->createMessage($conversation, 'user', $message);
            }

            // Get conversation context
            $context = $this->buildContext($conversation);

            // Send to AI provider
            $providerInstance = $this->getProviderInstance($provider);
            $response = $providerInstance->sendMessage($message, $context, $provider);

            // Create assistant message (only if conversation saving is enabled, check global config first)
            $assistantMessage = null;
            if ($saveConversations && $conversation->exists) {
                $assistantMessage = $this->createMessage(
                    $conversation,
                    'assistant',
                    $response['content'],
                    [
                        'provider_used' => $provider->slug,
                        'model_used' => $response['model'] ?? null,
                        'tokens_used' => $response['tokens'] ?? null,
                        'cost' => $response['cost'] ?? null,
                    ]
                );
            } else {
                // Create a temporary message object for non-persistent conversations
                $assistantMessage = new \App\Models\AI\Message([
                    'uuid' => \Str::uuid(),
                    'conversation_id' => $conversation->id ?? null,
                    'user_id' => null,
                    'role' => 'assistant',
                    'content' => $response['content'],
                    'metadata' => [
                        'provider_used' => $provider->slug,
                        'model_used' => $response['model'] ?? null,
                        'tokens_used' => $response['tokens'] ?? null,
                        'cost' => $response['cost'] ?? null,
                    ],
                    'provider_used' => $provider->slug,
                    'tokens_used' => $response['tokens'] ?? null,
                    'cost' => $response['cost'] ?? null,
                    'created_at' => now(),
                ]);
            }

            // Update conversation
            $conversation->updateLastMessageTime();

            // Log usage
            $this->logUsage([
                'team_id' => $conversation->team_id,
                'user_id' => $conversation->user_id,
                'conversation_id' => $conversation->id,
                'message_id' => $assistantMessage->id,
                'action_type' => $conversation->type,
                'provider_used' => $provider->slug,
                'model_used' => $response['model'] ?? null,
                'tokens_used' => $response['tokens'] ?? null,
                'cost' => $response['cost'] ?? null,
                'is_successful' => true,
                'response_time_ms' => (int)((microtime(true) - $startTime) * 1000),
            ]);

            return $assistantMessage;

        } catch (\Exception $e) {
            Log::error('AI Service Error', [
                'provider' => $provider->slug,
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage(),
            ]);

            // Create error message
            $errorMessage = $this->createMessage(
                $conversation,
                'assistant',
                'I apologize, but I encountered an error while processing your request. Please try again.',
                [
                    'provider_used' => $provider->slug,
                    'error' => $e->getMessage(),
                ],
                true,
                $e->getMessage()
            );

            // Log failed usage
            $this->logUsage([
                'team_id' => $conversation->team_id,
                'user_id' => $conversation->user_id,
                'conversation_id' => $conversation->id,
                'message_id' => $errorMessage->id,
                'action_type' => $conversation->type,
                'provider_used' => $provider->slug,
                'is_successful' => false,
                'error_message' => $e->getMessage(),
                'response_time_ms' => (int)((microtime(true) - $startTime) * 1000),
            ]);

            // Try fallback provider if available
            if (!$preferredProvider) {
                $fallbackProvider = $this->getAvailableProvider($conversation, [$provider->id]);
                if ($fallbackProvider) {
                    return $this->sendMessage($message, $conversation, $fallbackProvider);
                }
            }

            throw $e;
        }
    }

    private function getAvailableProvider(Conversation $conversation, array $excludeIds = []): ?Provider
    {
        // First, try to get tenant-specific providers
        $tenantProvider = Provider::where('team_id', $conversation->team_id)
            ->where('is_active', true)
            ->when(!empty($excludeIds), function ($query) use ($excludeIds) {
                $query->whereNotIn('id', $excludeIds);
            })
            ->orderBy('priority', 'desc')
            ->first();

        if ($tenantProvider) {
            return $tenantProvider;
        }

        // If no tenant provider, try global providers from .env
        $globalProviders = GlobalProviderService::getAvailableGlobalProviders();
        if (!empty($globalProviders)) {
            // Create a virtual Provider object from global config
            $globalProvider = $globalProviders[0]; // Highest priority
            return $this->createVirtualProvider($globalProvider);
        }

        return null;
    }

    /**
     * Create a virtual Provider object from global configuration
     */
    private function createVirtualProvider(array $globalConfig): Provider
    {
        $provider = new Provider();
        $provider->name = $globalConfig['name'];
        $provider->type = $globalConfig['type'];
        $provider->description = $globalConfig['description'];
        $provider->api_endpoint = $globalConfig['api_endpoint'];
        $provider->is_active = true;
        $provider->is_global = true;
        $provider->priority = $globalConfig['priority'];
        $provider->capabilities = $globalConfig['capabilities'];
        $provider->team_id = null;

        // Set the configuration including API key
        $config = [
            'api_key' => $globalConfig['api_key'],
            'api_endpoint' => $globalConfig['api_endpoint'],
            'capabilities' => $globalConfig['capabilities'],
            'models' => $globalConfig['models'],
            'default_model' => $globalConfig['models'][0] ?? null,
            'max_tokens' => 4000,
            'temperature' => 0.7,
        ];

        $provider->config = $config;

        return $provider;
    }

    private function getProviderInstance(Provider $provider)
    {
        $providerClass = $this->providers[$provider->type] ?? null;
        
        if (!$providerClass || !class_exists($providerClass)) {
            throw new \Exception("Provider {$provider->type} not supported");
        }

        return new $providerClass();
    }

    private function buildContext(Conversation $conversation): array
    {
        $messages = $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get()
            ->reverse()
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                ];
            })
            ->toArray();

        return [
            'conversation_type' => $conversation->type,
            'context' => $conversation->context,
            'context_data' => $conversation->context_data,
            'messages' => $messages,
        ];
    }

    private function createMessage(
        Conversation $conversation,
        string $role,
        string $content,
        array $metadata = [],
        bool $isError = false,
        ?string $errorMessage = null
    ): Message {
        return Message::create([
            'conversation_id' => $conversation->id,
            'user_id' => $role === 'user' ? $conversation->user_id : null,
            'role' => $role,
            'content' => $content,
            'metadata' => $metadata,
            'provider_used' => $metadata['provider_used'] ?? null,
            'tokens_used' => $metadata['tokens_used'] ?? null,
            'cost' => $metadata['cost'] ?? null,
            'is_error' => $isError,
            'error_message' => $errorMessage,
        ]);
    }

    private function logUsage(array $data): void
    {
        UsageLog::create($data);
    }

    public function testProvider(Provider $provider): array
    {
        $providerInstance = $this->getProviderInstance($provider);
        return $providerInstance->testConnection($provider);
    }
}
