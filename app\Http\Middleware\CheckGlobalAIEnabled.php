<?php

namespace App\Http\Middleware;

use App\Services\AI\GlobalProviderService;
use Closure;
use Illuminate\Http\Request;

class CheckGlobalAIEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if AI is globally disabled
        if (!GlobalProviderService::isGloballyEnabled()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'AI features are currently disabled by the administrator.',
                    'error' => 'AI_GLOBALLY_DISABLED'
                ], 503);
            }

            // For web requests, redirect with error message
            return redirect()->route('dashboard')->with('error', 'AI features are currently disabled by the administrator.');
        }

        return $next($request);
    }
}
