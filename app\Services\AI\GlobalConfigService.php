<?php

namespace App\Services\AI;

class GlobalConfigService
{
    /**
     * Get effective AI configuration (global .env overrides database config)
     */
    public static function getEffectiveConfig(): array
    {
        $globalConfig = GlobalProviderService::getGlobalConfig();
        $databaseConfig = config('config.ai', []);
        
        // Global config takes precedence over database config
        return array_merge($databaseConfig, array_filter($globalConfig, function($value) {
            return $value !== null && $value !== '';
        }));
    }

    /**
     * Check if a specific AI feature is enabled
     */
    public static function isFeatureEnabled(string $feature): bool
    {
        return GlobalProviderService::getConfigValue($feature, false);
    }

    /**
     * Get rate limit configuration
     */
    public static function getRateLimits(): array
    {
        return [
            'enabled' => GlobalProviderService::getConfigValue('rate_limit_enabled', false),
            'requests_per_minute' => GlobalProviderService::getConfigValue('rate_limit_requests_per_minute', 60),
            'requests_per_hour' => GlobalProviderService::getConfigValue('rate_limit_requests_per_hour', 1000),
            'requests_per_day' => GlobalProviderService::getConfigValue('rate_limit_requests_per_day', 10000),
        ];
    }

    /**
     * Get academic settings
     */
    public static function getAcademicSettings(): array
    {
        return [
            'curriculum_focus' => GlobalProviderService::getConfigValue('academic_curriculum_focus', 'nigerian_cambridge'),
            'default_level' => GlobalProviderService::getConfigValue('academic_default_level', 'sss'),
        ];
    }

    /**
     * Get conversation settings
     */
    public static function getConversationSettings(): array
    {
        return [
            'save_conversations' => GlobalProviderService::getConfigValue('save_conversations', true),
            'retention_days' => GlobalProviderService::getConfigValue('conversation_retention_days', 90),
            'max_conversation_length' => GlobalProviderService::getConfigValue('max_conversation_length', 50),
        ];
    }

    /**
     * Get token and request limits
     */
    public static function getRequestLimits(): array
    {
        return [
            'max_tokens_per_request' => GlobalProviderService::getConfigValue('max_tokens_per_request', 4000),
            'max_conversation_length' => GlobalProviderService::getConfigValue('max_conversation_length', 50),
        ];
    }

    /**
     * Get default provider from global config
     */
    public static function getDefaultProvider(): ?string
    {
        return GlobalProviderService::getConfigValue('default_provider', null);
    }

    /**
     * Check if fallback is enabled
     */
    public static function isFallbackEnabled(): bool
    {
        return GlobalProviderService::getConfigValue('fallback_enabled', true);
    }

    /**
     * Get all global overrides that are active
     */
    public static function getActiveGlobalOverrides(): array
    {
        $globalConfig = GlobalProviderService::getGlobalConfig();
        $activeOverrides = [];

        foreach ($globalConfig as $key => $value) {
            if ($value !== null && $value !== '') {
                $activeOverrides[$key] = $value;
            }
        }

        return $activeOverrides;
    }

    /**
     * Check if global configuration is overriding database config
     */
    public static function hasGlobalOverrides(): bool
    {
        return !empty(static::getActiveGlobalOverrides());
    }
}
