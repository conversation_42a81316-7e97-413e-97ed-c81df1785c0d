import { mutations, actions, getters } from "@stores/global"
import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/ai/assistant",
    formErrors: {},
    currentConversation: null,
    isProcessing: false,
    isExecutingAction: false,
})

const assistant = {
    namespaced: true,
    state: initialState,
    modules: {},
    mutations: {
        ...mutations,
        SET_CURRENT_CONVERSATION(state, conversationId) {
            state.currentConversation = conversationId
        },
        SET_PROCESSING(state, isProcessing) {
            state.isProcessing = isProcessing
        },
        SET_EXECUTING_ACTION(state, isExecutingAction) {
            state.isExecutingAction = isExecutingAction
        },
    },
    actions: {
        ...actions,
        async processCommand({ state, commit }, payload) {
            return Api.custom({
                url: "/app/ai/assistant/command",
                method: "POST",
                data: payload,
            })
                .then((response) => {
                    return response
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        async executeAction({ state, commit }, payload) {
            return Api.custom({
                url: "/app/ai/assistant/action",
                method: "POST",
                data: payload,
            })
                .then((response) => {
                    toast.success(response.message)
                    return response
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
    },
    getters: {
        ...getters,
        currentConversation: (state) => state.currentConversation,
        isProcessing: (state) => state.isProcessing,
        isExecutingAction: (state) => state.isExecutingAction,
    },
}

export default assistant
