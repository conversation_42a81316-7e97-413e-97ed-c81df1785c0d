<?php

namespace App\Console\Commands;

use App\Actions\Saas\SwitchDatabase;
use App\Concerns\GetTenantsForCommand;
use Illuminate\Console\Command;

class SyncRolePermission extends Command
{
    use GetTenantsForCommand;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:role-permission {tenant?* : ID of Tenants} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync roles, permissions and assign permissions to roles based on permission.json';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $force = $this->option('force');

        if (\App::environment('production') && ! $force) {
            $this->error('Could not sync in production mode');
            return 1;
        }

        $tenantIds = $this->argument('tenant');

        $tenants = $this->getTenants($tenantIds);

        $bar = $this->output->createProgressBar(count($tenants));
        $bar->start();

        activity()->disableLogging();

        foreach ($tenants as $tenant) {
            $this->newLine();
            $this->info("Syncing roles and permissions for tenant: {$tenant->id}");

            (new SwitchDatabase)->execute($tenant->database);

            \Artisan::call('cache:clear');

            // Sync in the correct order
            $this->info('  - Syncing roles...');
            \Artisan::call('db:seed', ['--class' => 'RoleSeeder', '--force' => $force ? true : false]);
            
            $this->info('  - Syncing permissions...');
            \Artisan::call('db:seed', ['--class' => 'PermissionSeeder', '--force' => $force ? true : false]);
            
            $this->info('  - Assigning permissions to roles...');
            \Artisan::call('db:seed', ['--class' => 'AssignPermissionSeeder', '--force' => $force ? true : false]);

            $this->info("Sync complete for tenant: {$tenant->id}");

            $bar->advance();
            $this->newLine();

            (new SwitchDatabase)->execute();
        }

        $bar->finish();

        activity()->enableLogging();

        $this->info('Roles and permissions synced successfully.');
        
        return 0;
    }
}
