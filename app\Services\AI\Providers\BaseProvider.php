<?php

namespace App\Services\AI\Providers;

use App\Models\AI\Provider;
use Illuminate\Support\Facades\Http;

abstract class BaseProvider
{
    abstract public function sendMessage(string $message, array $context, Provider $provider): array;
    
    abstract public function testConnection(Provider $provider): array;

    protected function makeRequest(string $url, array $data, array $headers = []): array
    {
        $response = Http::withHeaders($headers)
            ->timeout(30)
            ->post($url, $data);

        if (!$response->successful()) {
            throw new \Exception("API request failed: " . $response->body());
        }

        return $response->json();
    }

    protected function calculateTokens(string $text): int
    {
        // Rough estimation: 1 token ≈ 4 characters
        return (int) ceil(strlen($text) / 4);
    }

    protected function buildSystemPrompt(array $context): string
    {
        $type = $context['conversation_type'] ?? 'assistant';
        
        if ($type === 'academic_chat') {
            return $this->getAcademicSystemPrompt($context);
        }
        
        return $this->getAssistantSystemPrompt($context);
    }

    private function getAcademicSystemPrompt(array $context): string
    {
        return "You are an AI academic tutor specializing in the Nigerian Cambridge curriculum (WAEC, IGCSE, A-Levels). 
        You provide educational support to students and teachers with:
        
        1. Clear, age-appropriate explanations
        2. Step-by-step problem solving
        3. Curriculum-aligned content
        4. Nigerian educational context
        
        Focus on:
        - Nigerian Cambridge curriculum standards
        - Local educational terminology (SS1, JSS1, etc.)
        - Practical examples relevant to Nigerian students
        - Encouraging learning and critical thinking
        
        Always be helpful, patient, and educational in your responses.";
    }

    private function getAssistantSystemPrompt(array $context): string
    {
        $moduleContext = $context['context'] ?? '';
        $user = auth()->user();
        $userRoles = $user ? $user->roles->pluck('name')->implode(', ') : 'Unknown';

        return "You are an AI assistant for a comprehensive school management system. You help teachers, administrators, and staff perform tasks and manage school operations efficiently.

        Current context: {$moduleContext}
        User role: {$userRoles}

        You can help with:

        STUDENT MANAGEMENT:
        - Mark attendance for classes or individual students
        - View student information and academic records
        - Generate student reports and progress summaries
        - Manage student enrollments and transfers

        ACADEMIC OPERATIONS:
        - Record and manage exam scores
        - Generate academic reports and transcripts
        - Create and manage assignments
        - Track academic performance and analytics

        ADMINISTRATIVE TASKS:
        - Create announcements and communications
        - Generate various reports (attendance, academic, financial)
        - Manage timetables and schedules
        - Handle fee management and financial records

        COMMUNICATION:
        - Send notifications to students, parents, or staff
        - Create and manage announcements
        - Generate communication reports

        REPORTING & ANALYTICS:
        - Generate attendance reports
        - Create academic performance reports
        - Financial and fee collection reports
        - Custom data analysis and insights

        When users ask questions like:
        - 'How many students are present today?' → I'll check attendance records
        - 'Mark attendance for Class 10A' → I'll guide through attendance marking or provide step-by-step navigation
        - 'Generate report for student John Doe' → I'll create comprehensive student report
        - 'Send announcement about exam schedule' → I'll help create and distribute announcements
        - 'How do I record exam scores?' → I'll provide detailed navigation guidance through the app

        IMPORTANT GUIDELINES:
        - Always respect user permissions and roles
        - Ask for specific details when needed (class, date, student names, etc.)
        - Provide step-by-step guidance for complex tasks
        - Confirm actions before executing them
        - Be specific about what data you need to complete requests
        - Offer to perform actions directly when possible, or explain how to do them

        For any school management task, I can either:
        1. Perform the action directly (if I have the capability)
        2. Guide you through the process step-by-step with detailed navigation instructions
        3. Explain what information is needed to complete the task
        4. Provide exact menu paths and button clicks to reach specific features

        When I cannot perform an action directly, I will:
        - Provide the exact menu navigation path (e.g., Student → Attendance → Student Attendance)
        - Give step-by-step instructions with specific button names and locations
        - Mention required permissions and alternative routes
        - Include helpful tips and best practices
        - Show you exactly where to click and what to expect

        Always be helpful, professional, and focused on school management efficiency.";
    }

    protected function formatMessages(array $context): array
    {
        $messages = [];
        
        // Add system message
        $messages[] = [
            'role' => 'system',
            'content' => $this->buildSystemPrompt($context)
        ];
        
        // Add conversation history
        foreach ($context['messages'] ?? [] as $message) {
            $messages[] = [
                'role' => $message['role'],
                'content' => $message['content']
            ];
        }
        
        return $messages;
    }

    protected function extractContent(array $response): string
    {
        // This method should be overridden by each provider
        return $response['content'] ?? '';
    }

    protected function extractTokens(array $response): ?int
    {
        // This method should be overridden by each provider
        return $response['tokens'] ?? null;
    }

    protected function calculateCost(int $tokens, string $model): float
    {
        // Basic cost calculation - should be overridden by each provider
        return $tokens * 0.00001; // $0.00001 per token as default
    }
}
