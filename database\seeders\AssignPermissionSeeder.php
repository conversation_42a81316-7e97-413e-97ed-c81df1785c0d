<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AssignPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $acl = Arr::getVar('permission');
        $system_permissions = Arr::get($acl, 'permissions', []);

        $roles = Role::where('name', '!=', 'admin')->get();
        $permissions = Permission::all();

        // Clear existing role permissions (except admin)
        $roleIds = $roles->pluck('id')->toArray();
        \DB::table('role_has_permissions')->whereIn('role_id', $roleIds)->delete();

        $role_permission = [];
        $unique_combinations = [];

        foreach ($system_permissions as $permission_group) {
            foreach ($permission_group as $name => $assigned_roles) {
                $permission = $permissions->firstWhere('name', $name);
                if (!$permission) {
                    continue; // Skip if permission doesn't exist
                }

                foreach ($assigned_roles as $roleName) {
                    $role = $roles->firstWhere('name', $roleName);
                    if ($role) {
                        $combination_key = $permission->id . '-' . $role->id;

                        // Only add if this combination doesn't already exist
                        if (!isset($unique_combinations[$combination_key])) {
                            $unique_combinations[$combination_key] = true;
                            $role_permission[] = [
                                'permission_id' => $permission->id,
                                'role_id' => $role->id,
                            ];
                        }
                    }
                }
            }
        }

        if (!empty($role_permission)) {
            \DB::table('role_has_permissions')->insert($role_permission);
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
