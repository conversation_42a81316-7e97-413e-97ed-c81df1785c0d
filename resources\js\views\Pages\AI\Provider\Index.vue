<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('ai.provider.providers')"
                :navs="[{ label: $trans('ai.ai'), path: 'AIIndex' }]"
            >
                <PageHeaderAction
                    url="ai/providers/"
                    name="AIProviders"
                    :title="$trans('ai.provider.providers')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <Filter
                    @hide="showFilter = false"
                />
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="providers.headers"
                :meta="providers.meta"
                module="ai.provider"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow v-for="provider in providers.data" :key="provider.uuid">
                    <DataCell name="name">
                        <div class="flex items-center">
                            <span class="w-8 h-8 rounded-full flex items-center justify-center mr-3" :style="{ backgroundColor: provider.type_color }">
                                <i :class="provider.type_icon" class="text-white"></i>
                            </span>
                            <div>
                                <div class="font-medium">{{ provider.name }}</div>
                                <div class="text-sm text-gray-500">{{ provider.description }}</div>
                            </div>
                        </div>
                    </DataCell>
                    <DataCell name="type">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="provider.type_class">
                            {{ provider.type_name }}
                        </span>
                    </DataCell>
                    <DataCell name="status">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="provider.status_class">
                            {{ provider.status_name }}
                        </span>
                    </DataCell>
                    <DataCell name="is_default">
                        <span v-if="provider.is_default" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ $trans('general.yes') }}
                        </span>
                        <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{ $trans('general.no') }}
                        </span>
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-arrow-circle-right"
                                @click="router.push({ name: 'AIProviderShow', params: { uuid: provider.uuid } })"
                            >
                                {{ $trans('general.show') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                v-if="perform('ai-provider:edit')"
                                icon="fas fa-edit"
                                @click="router.push({ name: 'AIProviderEdit', params: { uuid: provider.uuid } })"
                            >
                                {{ $trans('general.edit') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                v-if="perform('ai-provider:delete')"
                                icon="fas fa-trash"
                                @click="emitter.emit('deleteItem', { uuid: provider.uuid })"
                            >
                                {{ $trans('general.delete') }}
                            </FloatingMenuItem>
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
                <template #actionButton>
                    <BaseButton
                        v-if="perform('ai-provider:create')"
                        @click="router.push({ name: 'AIProviderCreate' })"
                    >
                        {{ $trans('global.add', { attribute: $trans('ai.provider.provider') }) }}
                    </BaseButton>
                </template>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: 'AIProviderIndex',
}
</script>

<script setup>
import { ref, reactive, inject } from 'vue'
import { useRouter } from 'vue-router'
import { perform } from '@core/helpers/action'
import Filter from './Filter.vue'

const router = useRouter()
const emitter = inject('emitter')
const $trans = inject('$trans')

const showFilter = ref(false)
const initUrl = 'ai/provider/'

let userActions = ['filter']
if (perform('ai-provider:create')) {
    userActions.unshift('create')
}

let dropdownActions = []
if (perform('ai-provider:export')) {
    dropdownActions = ['print', 'pdf', 'excel']
}

const providers = reactive({})

const setItems = (data) => {
    Object.assign(providers, data)
}
</script>