export default [
    {
        path: 'ai',
        name: 'AIIndex',
        redirect: { name: 'AIDashboard' },
        meta: {
            moduleName: 'ai',
            label: 'ai.ai',
            icon: 'fas fa-robot',
            permissions: ['ai-provider:read', 'ai-conversation:read', 'ai-knowledge:read'],
        },
        component: {
            template: '<router-view></router-view>',
        },
        children: [
            {
                path: '',
                name: 'AIDashboard',
                component: () => import('@views/Pages/AI/Dashboard.vue'),
                meta: {
                    isNotNav: true, //Hide 
                    trans: 'global.dashboard',
                    label: 'ai.ai',
                    icon: 'fas fa-tachometer-alt',
                    permissions: ['ai-provider:read', 'ai-conversation:read', 'ai-knowledge:read'],
                },
            },
            {
                path: 'providers',
                name: 'AIProviders',
                redirect: { name: 'AIProviderList' },
                meta: {
                    isNotNav: true, //Hide 
                    label: 'ai.provider.providers',
                    icon: 'fas fa-server',
                    permissions: ['ai-provider:read'],
                    keySearch: true,
                },
            },
            {
                path: 'conversations',
                name: 'AIConversations',
                redirect: { name: 'AIConversationList' },
                meta: {
                    label: 'ai.conversation.conversations',
                    icon: 'fas fa-comments',
                    permissions: ['ai-conversation:read'],
                    keySearch: true,
                },
            },
            {
                path: 'knowledge',
                name: 'AIKnowledge',
                redirect: { name: 'AIKnowledgeList' },
                meta: {
                    isNotNav: true, //Hide 
                    label: 'ai.knowledge.knowledge_base',
                    icon: 'fas fa-brain',
                    permissions: ['ai-knowledge:read'],
                    keySearch: true,
                },
            },
            {
                path: 'assistant',
                name: 'AIAssistant',
                component: () => import('@views/Pages/AI/Assistant/Index.vue'),
                meta: {
                    isNotNav: true, //Hide 
                    trans: 'global.assistant',
                    label: 'ai.assistant.assistant',
                    icon: 'fas fa-robot',
                    permissions: ['ai:use-assistant'],
                },
            },
            {
                path: 'academic',
                name: 'AIAcademic',
                component: () => import('@views/Pages/AI/Academic/Index.vue'),
                meta: {
                    trans: 'global.academic',
                    label: 'ai.academic.academic_chat',
                    icon: 'fas fa-graduation-cap',
                    permissions: ['ai:use-academic-chat'],
                },
            },
            {
                path: 'config',
                name: 'AIConfig',
                component: () => import('@views/Pages/AI/Config/Index.vue'),
                meta: {
                    isNotNav: true,
                    type: 'config',
                    action: 'config',
                    trans: 'global.config',
                    label: 'config.config',
                    icon: 'far fa-building',
                    permissions: ['ai:manage-config'],
                },
            },
        ],
    },
    // Separate route for AI Providers with UUID operations
    {
        path: 'ai-providers',
        name: 'AIProvider',
        redirect: { name: 'AIProviderList' },
        meta: {
            isNotNav: true,
            label: 'ai.provider.provider',
            icon: 'fas fa-server',
            hideChildren: true,
            permissions: ['ai-provider:read'],
        },
        component: {
            template: '<router-view></router-view>',
        },
        children: [
            {
                path: '',
                name: 'AIProviderList',
                meta: {
                    trans: 'global.list',
                    label: 'ai.provider.providers',
                    icon: 'fas fa-list',
                    keySearch: true,
                },
                component: () => import('@views/Pages/AI/Provider/Index.vue'),
            },
            {
                path: 'create',
                name: 'AIProviderCreate',
                component: () => import('@views/Pages/AI/Provider/Action.vue'),
                meta: {
                    type: 'create',
                    action: 'create',
                    trans: 'global.add',
                    label: 'ai.provider.provider',
                    icon: 'fas fa-plus',
                    permissions: ['ai-provider:create'],
                },
            },
            {
                path: ':uuid/edit',
                name: 'AIProviderEdit',
                component: () => import('@views/Pages/AI/Provider/Action.vue'),
                meta: {
                    type: 'edit',
                    action: 'update',
                    trans: 'global.edit',
                    label: 'ai.provider.provider',
                    icon: 'fas fa-edit',
                    permissions: ['ai-provider:edit'],
                },
            },
            {
                path: ':uuid',
                name: 'AIProviderShow',
                component: () => import('@views/Pages/AI/Provider/Show.vue'),
                meta: {
                    trans: 'global.detail',
                    label: 'ai.provider.provider',
                    icon: 'fas fa-eye',
                    permissions: ['ai-provider:read'],
                },
            },
        ],
    },
    // Separate route for AI Conversations with UUID operations
    {
        path: 'ai-conversations',
        name: 'AIConversation',
        redirect: { name: 'AIConversationList' },
        meta: {
            isNotNav: true,
            label: 'ai.conversation.conversation',
            icon: 'fas fa-comments',
            hideChildren: true,
            permissions: ['ai-conversation:read'],
        },
        component: {
            template: '<router-view></router-view>',
        },
        children: [
            {
                path: '',
                name: 'AIConversationList',
                meta: {
                    trans: 'global.list',
                    label: 'ai.conversation.conversations',
                    icon: 'fas fa-list',
                    keySearch: true,
                },
                component: () => import('@views/Pages/AI/Conversation/Index.vue'),
            },
            {
                path: ':uuid',
                name: 'AIConversationShow',
                component: () => import('@views/Pages/AI/Conversation/Show.vue'),
                meta: {
                    trans: 'global.detail',
                    label: 'ai.conversation.conversation',
                    icon: 'fas fa-eye',
                    permissions: ['ai-conversation:read'],
                },
            },
        ],
    },
    // Separate route for AI Knowledge with UUID operations
    {
        path: 'ai-knowledge',
        name: 'AIKnowledge',
        redirect: { name: 'AIKnowledgeList' },
        meta: {
            isNotNav: true,
            label: 'ai.knowledge.knowledge',
            icon: 'fas fa-brain',
            hideChildren: true,
            permissions: ['ai-knowledge:read'],
        },
        component: {
            template: '<router-view></router-view>',
        },
        children: [
            {
                path: '',
                name: 'AIKnowledgeList',
                meta: {
                    trans: 'global.list',
                    label: 'ai.knowledge.knowledge_base',
                    icon: 'fas fa-list',
                    keySearch: true,
                },
                component: () => import('@views/Pages/AI/Knowledge/Index.vue'),
            },
            {
                path: 'create',
                name: 'AIKnowledgeCreate',
                component: () => import('@views/Pages/AI/Knowledge/Action.vue'),
                meta: {
                    type: 'create',
                    action: 'create',
                    trans: 'global.add',
                    label: 'ai.knowledge.knowledge',
                    icon: 'fas fa-plus',
                    permissions: ['ai-knowledge:create'],
                },
            },
            {
                path: ':uuid/edit',
                name: 'AIKnowledgeEdit',
                component: () => import('@views/Pages/AI/Knowledge/Action.vue'),
                meta: {
                    type: 'edit',
                    action: 'update',
                    trans: 'global.edit',
                    label: 'ai.knowledge.knowledge',
                    icon: 'fas fa-edit',
                    permissions: ['ai-knowledge:edit'],
                },
            },
            {
                path: ':uuid',
                name: 'AIKnowledgeShow',
                component: () => import('@views/Pages/AI/Knowledge/Show.vue'),
                meta: {
                    trans: 'global.detail',
                    label: 'ai.knowledge.knowledge',
                    icon: 'fas fa-eye',
                    permissions: ['ai-knowledge:read'],
                },
            },
        ],
    },
];