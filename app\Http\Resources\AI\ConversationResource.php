<?php

namespace App\Http\Resources\AI;

use App\Enums\AI\ConversationType;
use Illuminate\Http\Resources\Json\JsonResource;

class ConversationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $conversationType = ConversationType::tryFrom($this->type);

        return [
            'uuid' => $this->uuid,
            'type' => $this->type,
            'type_name' => $conversationType ? $conversationType->getName() : $this->type,
            'type_icon' => $conversationType ? $conversationType->getIcon() : 'fas fa-comment',
            'type_color' => $conversationType ? $conversationType->getColor() : 'gray',
            'title' => $this->title,
            'context' => $this->context,
            'context_data' => $this->context_data,
            'last_message_at' => $this->last_message_at ? \Cal::dateTime($this->last_message_at) : null,
            'user' => $this->when($this->user, function () {
                return [
                    'uuid' => $this->user->uuid,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),
            'latest_message' => $this->when($this->latestMessage->first(), function () {
                $message = $this->latestMessage->first();
                return [
                    'uuid' => $message->uuid,
                    'role' => $message->role,
                    'content' => substr($message->content, 0, 100) . (strlen($message->content) > 100 ? '...' : ''),
                    'created_at' => \Cal::dateTime($message->created_at),
                ];
            }),
            'messages_count' => $this->when($this->messages_count !== null, $this->messages_count),
            'created_at' => \Cal::dateTime($this->created_at),
            'updated_at' => \Cal::dateTime($this->updated_at),
        ];
    }
}
