<?php

namespace App\Services\AI;

use App\Models\AI\Conversation;
use App\Models\AI\Message;
use Illuminate\Http\Request;

class AcademicChatService
{
    private ConversationService $conversationService;
    private AIService $aiService;
    private KnowledgeBaseService $knowledgeBaseService;

    public function __construct(
        ConversationService $conversationService,
        AIService $aiService,
        KnowledgeBaseService $knowledgeBaseService
    ) {
        $this->conversationService = $conversationService;
        $this->aiService = $aiService;
        $this->knowledgeBaseService = $knowledgeBaseService;
    }

    public function askQuestion(Request $request): array
    {
        $question = $request->question;
        $subject = $request->subject;
        $level = $request->level;
        $category = $request->category ?? 'nigerian_cambridge';

        // Find or create academic chat conversation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'academic',
            [
                'subject' => $subject,
                'level' => $level,
                'category' => $category,
            ]
        );

        // Get relevant knowledge from knowledge base
        $relevantKnowledge = $this->knowledgeBaseService->getRelevantKnowledge(
            $question,
            [
                'subject' => $subject,
                'level' => $level,
                'category' => $category,
            ]
        );

        // Enhance question with knowledge base context
        $enhancedQuestion = $this->enhanceQuestionWithKnowledge($question, $relevantKnowledge, [
            'subject' => $subject,
            'level' => $level,
            'category' => $category,
        ]);

        // Send to AI
        $response = $this->aiService->sendMessage($enhancedQuestion, $conversation);

        // Check if we should save this as learning material
        $shouldSave = $this->shouldSaveLearningMaterial($question, $response->content);

        return [
            'conversation_id' => $conversation->uuid,
            'message' => $response,
            'relevant_knowledge' => $relevantKnowledge,
            'context' => [
                'subject' => $subject,
                'level' => $level,
                'category' => $category,
            ],
            'save_suggestion' => $shouldSave,
        ];
    }

    public function generateLessonPlan(Request $request): array
    {
        $topic = $request->topic;
        $subject = $request->subject;
        $level = $request->level;
        $duration = $request->duration ?? 40; // minutes
        $objectives = $request->objectives ?? [];

        // Create specific conversation for lesson planning
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'lesson_planning',
            [
                'subject' => $subject,
                'level' => $level,
                'topic' => $topic,
            ]
        );

        // Build lesson plan prompt
        $prompt = $this->buildLessonPlanPrompt($topic, $subject, $level, $duration, $objectives);
        
        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        return [
            'conversation_id' => $conversation->uuid,
            'lesson_plan' => $response,
            'context' => [
                'topic' => $topic,
                'subject' => $subject,
                'level' => $level,
                'duration' => $duration,
                'objectives' => $objectives,
            ],
            'save_to_resources' => true,
        ];
    }

    public function generateQuiz(Request $request): array
    {
        $topic = $request->topic;
        $subject = $request->subject;
        $level = $request->level;
        $questionCount = $request->question_count ?? 10;
        $questionTypes = $request->question_types ?? ['multiple_choice', 'short_answer'];

        // Create specific conversation for quiz generation
        $conversation = $this->conversationService->findOrCreateConversation(
            'academic_chat',
            'quiz_generation',
            [
                'subject' => $subject,
                'level' => $level,
                'topic' => $topic,
            ]
        );

        // Build quiz generation prompt
        $prompt = $this->buildQuizPrompt($topic, $subject, $level, $questionCount, $questionTypes);

        // Send to AI
        $response = $this->aiService->sendMessage($prompt, $conversation);

        return [
            'conversation_id' => $conversation->uuid,
            'quiz' => $response,
            'context' => [
                'topic' => $topic,
                'subject' => $subject,
                'level' => $level,
                'question_count' => $questionCount,
                'question_types' => $questionTypes,
            ],
            'save_to_resources' => true,
        ];
    }

    private function enhanceQuestionWithKnowledge(string $question, array $knowledge, array $context): string
    {
        $enhancement = $question;
        
        $enhancement .= "\n\nContext:";
        $enhancement .= "\n- Subject: " . ($context['subject'] ?? 'General');
        $enhancement .= "\n- Level: " . ($context['level'] ?? 'General');
        $enhancement .= "\n- Curriculum: " . ($context['category'] ?? 'Nigerian Cambridge');

        if (!empty($knowledge)) {
            $enhancement .= "\n\nRelevant Knowledge Base Content:";
            foreach ($knowledge as $item) {
                $enhancement .= "\n\nTitle: " . $item['title'];
                $enhancement .= "\nContent: " . substr($item['content'], 0, 500) . '...';
            }
        }

        $enhancement .= "\n\nPlease provide a comprehensive answer that is appropriate for the specified level and curriculum.";

        return $enhancement;
    }

    private function buildLessonPlanPrompt(string $topic, string $subject, string $level, int $duration, array $objectives): string
    {
        $prompt = "Generate a detailed lesson plan and a comprehensive lesson note for the following:";
        $prompt .= "\n\nTopic: {$topic}";
        $prompt .= "\nSubject: {$subject}";
        $prompt .= "\nLevel: {$level}";
        $prompt .= "\nDuration: {$duration} minutes";
        
        if (!empty($objectives)) {
            $prompt .= "\nLearning Objectives:";
            foreach ($objectives as $objective) {
                $prompt .= "\n- {$objective}";
            }
        }

        $prompt .= "\n\nPlease include:";
        $prompt .= "\n1. Learning objectives (if not provided)";
        $prompt .= "\n2. Materials needed";
        $prompt .= "\n3. Introduction/warm-up activity";
        $prompt .= "\n4. Main lesson activities";
        $prompt .= "\n5. Assessment methods";
        $prompt .= "\n6. Conclusion/wrap-up";
        $prompt .= "\n7. Homework/extension activities";
        $prompt .= "\n\nAlign with Nigerian Cambridge curriculum standards.";

        return $prompt;
    }

    private function buildQuizPrompt(string $topic, string $subject, string $level, int $questionCount, array $questionTypes): string
    {
        $prompt = "Generate a quiz with the following specifications:";
        $prompt .= "\n\nTopic: {$topic}";
        $prompt .= "\nSubject: {$subject}";
        $prompt .= "\nLevel: {$level}";
        $prompt .= "\nNumber of Questions: {$questionCount}";
        $prompt .= "\nQuestion Types: " . implode(', ', $questionTypes);

        $prompt .= "\n\nRequirements:";
        $prompt .= "\n1. Questions should be appropriate for {$level} level";
        $prompt .= "\n2. Align with Nigerian Cambridge curriculum";
        $prompt .= "\n3. Include answer key";
        $prompt .= "\n4. Vary difficulty levels";
        $prompt .= "\n5. Use clear, unambiguous language";

        $prompt .= "\n\nFormat the output as a structured quiz with questions, options (for multiple choice), and answers.";

        return $prompt;
    }

    private function shouldSaveLearningMaterial(string $question, string $response): bool
    {
        // Simple heuristic to determine if this should be saved as learning material
        // Could be enhanced with more sophisticated logic
        
        $questionLength = strlen($question);
        $responseLength = strlen($response);
        
        // Save if it's a substantial Q&A
        return $questionLength > 20 && $responseLength > 100;
    }
}
