<template>
    <div>
        <PageHeader
            :title="$trans('ai.assistant.assistant')"
            :navs="[
                { label: $trans('ai.ai'), path: 'AIIndex' },
            ]"
        >
            <template #actions>
                <BaseButton
                    v-if="isVoiceSupported"
                    @click="toggleVoiceInput"
                    :design="isListening ? 'danger' : 'white'"
                    size="sm"
                >
                    <i :class="isListening ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
                    {{ isListening ? $trans('general.stop') : $trans('ai.voice_input') }}
                </BaseButton>
            </template>
        </PageHeader>

        <ParentTransition appear :visibility="true">
            <div class="px-4">
                <BaseCard>
                    <template #header>
                        <CardHeader
                            :title="$trans('ai.assistant.assistant')"
                            icon="fas fa-robot"
                        >
                            <template #actions>
                                <span v-if="route.query.conversationId" class="badge bg-info me-2">
                                    {{ $trans('ai.conversation.continuing_conversation') }}
                                </span>
                                <BaseButton
                                    @click="showHistoryModal = true"
                                    design="white"
                                    size="sm"
                                    v-tooltip="$trans('ai.conversation.conversations')"
                                >
                                    <i class="fas fa-history"></i>
                                </BaseButton>
                            </template>
                        </CardHeader>
                    </template>

                    <!-- Chat Messages Area -->
                    <div class="chat-container" ref="chatContainer">
                        <!-- Empty State -->
                        <div v-if="messages.length === 0" class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3 class="empty-state-title">{{ $trans('ai.assistant.welcome') }}</h3>
                            <p class="empty-state-subtitle">{{ $trans('ai.assistant.welcome_message') }}</p>

                            <!-- Quick Commands for Empty State -->
                            <div class="mt-4">
                                <p class="text-muted mb-2">{{ $trans('ai.assistant.quick_commands') }}:</p>
                                <div class="d-flex flex-wrap gap-2 justify-content-center">
                                    <BaseButton
                                        v-for="command in quickCommands"
                                        :key="command.text"
                                        @click="useQuickCommand(command.text)"
                                        design="white"
                                        size="sm"
                                        :disabled="isProcessing"
                                    >
                                        {{ command.text }}
                                    </BaseButton>
                                </div>
                            </div>
                        </div>

                        <!-- Messages -->
                        <div v-else class="messages-list">
                            <div v-for="message in messages" :key="message.id" class="message-wrapper">
                                <div :class="['message', message.role === 'user' ? 'message-user' : 'message-assistant']">
                                    <div class="message-avatar">
                                        <i :class="message.role === 'user' ? 'fas fa-user' : 'fas fa-robot'"></i>
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <div v-html="formatMessage(message.content)"></div>

                                            <!-- Action Buttons -->
                                            <div v-if="message.actions && message.actions.length > 0" class="message-actions">
                                                <TextMuted class="mb-2">{{ $trans('ai.assistant.suggested_actions') }}:</TextMuted>
                                                <div class="d-flex flex-wrap gap-1">
                                                    <BaseButton
                                                        v-for="action in message.actions"
                                                        :key="action.type"
                                                        @click="executeAction(action)"
                                                        design="white"
                                                        size="sm"
                                                        :disabled="executingAction"
                                                    >
                                                        {{ action.description }}
                                                    </BaseButton>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="message-time">
                                            <TextMuted>{{ moment(message.timestamp).format('HH:mm') }}</TextMuted>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Typing Indicator -->
                            <div v-if="isProcessing" class="message-wrapper">
                                <div class="message message-assistant">
                                    <div class="message-avatar">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <div class="typing-indicator">
                                                <span></span>
                                                <span></span>
                                                <span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <template #footer>
                        <div class="chat-input-area">
                            <!-- Quick Commands (only show when there are messages) -->
                            <div v-if="messages.length > 0" class="quick-commands mb-3">
                                <TextMuted class="mb-2">{{ $trans('ai.assistant.quick_commands') }}:</TextMuted>
                                <div class="d-flex flex-wrap gap-1">
                                    <BaseButton
                                        v-for="command in quickCommands"
                                        :key="command.text"
                                        @click="useQuickCommand(command.text)"
                                        design="white"
                                        size="sm"
                                        :disabled="isProcessing"
                                    >
                                        {{ command.text }}
                                    </BaseButton>
                                </div>
                            </div>

                            <form @submit.prevent="sendMessage" class="chat-form">
                                <div class="chat-input-container">
                                    <div class="chat-input-wrapper">
                                        <textarea
                                            v-model="currentMessage"
                                            :placeholder="$trans('ai.assistant.type_message')"
                                            :disabled="isProcessing"
                                            ref="messageInput"
                                            class="chat-input-field"
                                            rows="1"
                                            @keydown.enter.exact.prevent="sendMessage"
                                            @keydown.enter.shift.exact="addNewLine"
                                            @input="adjustTextareaHeight"
                                        ></textarea>
                                        <BaseButton
                                            type="submit"
                                            design="primary"
                                            :disabled="!currentMessage.trim() || isProcessing"
                                            class="chat-send-btn"
                                        >
                                            <i v-if="isProcessing" class="fas fa-spinner fa-spin"></i>
                                            <i v-else class="fas fa-paper-plane"></i>
                                        </BaseButton>
                                    </div>
                                    <div class="chat-input-hint">
                                        <TextMuted>{{ $trans('ai.assistant.input_hint') }}</TextMuted>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </template>
                </BaseCard>
            </div>
        </ParentTransition>

        <!-- Conversation History Modal -->
        <BaseModal
            :visibility="showHistoryModal"
            @close="showHistoryModal = false"
            size="lg"
        >
            <template #title>
                {{ $trans('ai.conversation.conversations') }}
            </template>

            <div v-if="loadingHistory" class="text-center py-4">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">{{ $trans('general.loading') }}</span>
                </div>
            </div>

            <div v-else-if="conversationHistory.length === 0" class="text-center py-4">
                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                <p class="text-muted">{{ $trans('general.no_records_found') }}</p>
            </div>

            <div v-else class="list-group">
                <div
                    v-for="conversation in conversationHistory"
                    :key="conversation.uuid"
                    class="list-group-item list-group-item-action d-flex justify-content-between align-items-start"
                    @click="continueConversation(conversation)"
                    style="cursor: pointer;"
                >
                    <div class="ms-2 me-auto">
                        <div class="fw-bold">{{ conversation.title }}</div>
                        <small class="text-muted">
                            {{ conversation.latestMessage ? conversation.latestMessage.content.substring(0, 100) + '...' : $trans('general.no_messages') }}
                        </small>
                        <br>
                        <small class="text-muted">
                            {{ moment(conversation.updatedAt).fromNow() }}
                        </small>
                    </div>
                    <span class="badge bg-primary rounded-pill">
                        {{ conversation.messagesCount || 0 }}
                    </span>
                </div>
            </div>

            <div class="mt-3 d-flex justify-content-end">
                <BaseButton
                    @click="showHistoryModal = false"
                    design="secondary"
                >
                    {{ $trans('general.close') }}
                </BaseButton>
            </div>
        </BaseModal>
    </div>
</template>

<script>
export default {
    name: 'AIAssistant',
}
</script>

<script setup>
import { ref, onMounted, nextTick, inject, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useToast } from 'vue-toastification'

const route = useRoute()
const router = useRouter()
const store = useStore()
const toast = useToast()

const $trans = inject('$trans')
const moment = inject('moment')

const messages = ref([])
const currentMessage = ref('')
const isListening = ref(false)
const isVoiceSupported = ref(false)
const recognition = ref(null)
const isProcessing = ref(false)
const executingAction = ref(false)
const chatContainer = ref(null)
const messageInput = ref(null)
const showHistoryModal = ref(false)
const conversationHistory = ref([])
const loadingHistory = ref(false)
const currentConversationId = ref(null)

const quickCommands = [
    { text: 'Mark attendance for today' },
    { text: 'Show student statistics' },
    { text: 'Create an announcement' },
    { text: 'Generate a report' },
    { text: 'Help me with...' },
]
const sendMessage = async () => {
    if (!currentMessage.value.trim()) return

    const userMessage = {
        id: Date.now(),
        role: 'user',
        content: currentMessage.value,
        timestamp: new Date(),
    }

    messages.value.push(userMessage)
    const messageText = currentMessage.value
    currentMessage.value = ''

    try {
        isProcessing.value = true
        // Parse contextData if it's a JSON string from URL query
        let contextData = {}
        if (route.query.contextData) {
            try {
                contextData = typeof route.query.contextData === 'string'
                    ? JSON.parse(route.query.contextData)
                    : route.query.contextData
            } catch (e) {
                console.warn('Failed to parse contextData from URL:', e)
                contextData = {}
            }
        }

        const response = await store.dispatch('ai/assistant/processCommand', {
            message: messageText,
            context: route.query.context || 'general',
            contextData: contextData,
        })

        const assistantMessage = {
            id: Date.now() + 1,
            role: 'assistant',
            content: response.result.message.content,
            actions: response.result.actions || [],
            timestamp: new Date(),
        }

        messages.value.push(assistantMessage)

        // Store conversation ID for future reference
        if (response.result.conversationId) {
            currentConversationId.value = response.result.conversationId

            // Update store with current conversation ID
            store.commit('ai/assistant/SET_CURRENT_CONVERSATION', response.result.conversationId)

            // Update URL to include conversation ID for persistence
            const newQuery = { ...route.query, conversationId: response.result.conversationId }
            router.replace({ query: newQuery })
        }

        scrollToBottom()
    } catch (error) {
        const errorMessage = {
            id: Date.now() + 1,
            role: 'assistant',
            content: 'I apologize, but I encountered an error while processing your request. Please try again.',
            timestamp: new Date(),
        }
        messages.value.push(errorMessage)
        toast.error(error.message || 'Failed to send message')
    } finally {
        isProcessing.value = false
    }
}

const executeAction = async (action) => {
    try {
        executingAction.value = true

        // Use the current conversation ID from multiple sources
        const conversationId = currentConversationId.value ||
                              route.query.conversationId ||
                              store.getters['ai/assistant/currentConversation']

        if (!conversationId) {
            throw new Error('No active conversation found. Please start a conversation first.')
        }

        const response = await store.dispatch('ai/assistant/executeAction', {
            action: action.type,
            parameters: action.parameters || {},
            conversationId: conversationId,
        })

        const resultMessage = {
            id: Date.now(),
            role: 'assistant',
            content: `Action "${action.description}" executed successfully: ${response.result.message}`,
            timestamp: new Date(),
        }

        messages.value.push(resultMessage)
        scrollToBottom()
        // Success toast is already shown by the store action
    } catch (error) {
        toast.error(error.message || 'Failed to execute action')
    } finally {
        executingAction.value = false
    }
}

const useQuickCommand = (command) => {
    currentMessage.value = command
    focusInput()
}

const formatMessage = (content) => {
    // Basic markdown-like formatting
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
    nextTick(() => {
        if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight
        }
    })
}

const focusInput = () => {
    nextTick(() => {
        if (messageInput.value) {
            messageInput.value.focus()
        }
    })
}

const addNewLine = () => {
    currentMessage.value += '\n'
    nextTick(() => {
        adjustTextareaHeight()
    })
}

const adjustTextareaHeight = () => {
    nextTick(() => {
        if (messageInput.value) {
            messageInput.value.style.height = 'auto'
            messageInput.value.style.height = Math.min(messageInput.value.scrollHeight, 120) + 'px'
        }
    })
}

const initializeVoiceRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        isVoiceSupported.value = true
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        recognition.value = new SpeechRecognition()
        recognition.value.continuous = false
        recognition.value.interimResults = false
        recognition.value.lang = 'en-US'

        recognition.value.onresult = (event) => {
            const transcript = event.results[0][0].transcript
            currentMessage.value = transcript
            isListening.value = false
        }

        recognition.value.onerror = () => {
            isListening.value = false
            toast.error('Voice recognition error')
        }

        recognition.value.onend = () => {
            isListening.value = false
        }
    }
}

const toggleVoiceInput = () => {
    if (!recognition.value) return

    if (isListening.value) {
        recognition.value.stop()
        isListening.value = false
    } else {
        recognition.value.start()
        isListening.value = true
    }
}

const loadConversationHistory = async () => {
    const conversationId = route.query.conversationId
    if (conversationId) {
        try {
            const response = await store.dispatch('ai/conversation/fetchMessages', {
                conversationUuid: conversationId,
            })

            // Convert backend messages to frontend format
            const conversationMessages = response.data.map(msg => ({
                id: msg.uuid,
                role: msg.role,
                content: msg.content,
                timestamp: new Date(msg.createdAt),
                actions: msg.metadata?.actions || [],
            }))

            messages.value = conversationMessages
            currentConversationId.value = conversationId

            // Update store with current conversation ID
            store.commit('ai/assistant/SET_CURRENT_CONVERSATION', conversationId)

            scrollToBottom()
        } catch (error) {
            toast.error('Failed to load conversation history')
        }
    }
}

const fetchConversationHistory = async () => {
    loadingHistory.value = true
    try {
        const response = await store.dispatch('ai/conversation/list', {
            type: 'assistant',
            perPage: 20,
        })
        conversationHistory.value = response.data
    } catch (error) {
        toast.error('Failed to load conversation history')
    } finally {
        loadingHistory.value = false
    }
}

const continueConversation = (conversation) => {
    showHistoryModal.value = false
    router.push({
        name: 'AIAssistant',
        query: { conversationId: conversation.uuid }
    })
}

// Watch for history modal opening
watch(showHistoryModal, (newValue) => {
    if (newValue) {
        fetchConversationHistory()
    }
})

onMounted(() => {
    initializeVoiceRecognition()
    loadConversationHistory()
    focusInput()
})
</script>

<style scoped>
/* Chat Container */
.chat-container {
    height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--tblr-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--tblr-body-color);
}

.empty-state-subtitle {
    color: var(--tblr-text-muted);
    margin-bottom: 0;
}

/* Messages List */
.messages-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Message Wrapper */
.message-wrapper {
    display: flex;
    width: 100%;
}

.message {
    display: flex;
    max-width: 80%;
    gap: 0.75rem;
}

.message-user {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-assistant {
    margin-right: auto;
}

/* Message Avatar */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.message-user .message-avatar {
    background: var(--tblr-primary);
    color: white;
}

.message-assistant .message-avatar {
    background: var(--tblr-secondary);
    color: white;
}

/* Message Content */
.message-content {
    flex: 1;
    min-width: 0;
}

.message-bubble {
    padding: 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
    line-height: 1.5;
}

.message-user .message-bubble {
    background: var(--tblr-bg-surface);
    border: 1px solid var(--tblr-border-color);
    color: var(--tblr-body-color);
    border-bottom-right-radius: 0.25rem;
}

.message-assistant .message-bubble {
    background: var(--tblr-bg-surface);
    border: 1px solid var(--tblr-border-color);
    color: var(--tblr-body-color);
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    margin-top: 0.25rem;
    font-size: 0.75rem;
}

.message-user .message-time {
    text-align: right;
}

/* Message Actions */
.message-actions {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.message-assistant .message-actions {
    border-top-color: var(--tblr-border-color);
}

/* Chat Input Area */
.chat-input-area {
    padding: 1.5rem;
    border-top: 1px solid var(--tblr-border-color);
    background: var(--tblr-bg-surface);
}

.chat-form {
    margin-bottom: 0;
}

.chat-input-container {
    width: 100%;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--tblr-border-color);
    border-radius: 1rem;
    background: var(--tblr-body-bg);
    transition: border-color 0.2s ease;
}

.chat-input-wrapper:focus-within {
    border-color: var(--tblr-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--tblr-primary-rgb), 0.25);
}

.chat-input-field {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--tblr-body-color);
    min-height: 1.5rem;
    max-height: 120px;
    overflow-y: auto;
}

.chat-input-field::placeholder {
    color: var(--tblr-text-muted);
}

.chat-send-btn {
    flex-shrink: 0;
    border-radius: 0.75rem;
    padding: 0.5rem 1rem;
}

.chat-input-hint {
    margin-top: 0.5rem;
    text-align: center;
}

/* Quick Commands */
.quick-commands {
    margin-bottom: 0;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--tblr-text-muted);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .message {
        max-width: 90%;
    }

    .chat-container {
        height: 500px;
    }

    .empty-state {
        padding: 1rem;
    }

    .empty-state-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
</style>
