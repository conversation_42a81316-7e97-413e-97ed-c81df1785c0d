<?php

namespace App\Enums\AI;

use App\Concerns\HasEnum;

enum ProviderType: string
{
    use HasEnum;

    case OPENAI = 'openai';
    case GEMINI = 'gemini';
    case DEEPSEEK = 'deepseek';
    case ANTHROPIC = 'anthropic';
    case XAI = 'xai';

    public static function getDetail(): array
    {
        return [
            self::OPENAI->value => [
                'name' => 'OpenAI',
                'description' => 'OpenAI GPT models for natural language processing',
                'api_endpoint' => 'https://api.openai.com/v1',
                'capabilities' => ['text', 'image', 'voice'],
                'models' => ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
                'priority' => 2,
            ],
            self::GEMINI->value => [
                'name' => 'Google Gemini',
                'description' => 'Google Gemini for conversational AI and content generation',
                'api_endpoint' => 'https://generativelanguage.googleapis.com/v1beta',
                'capabilities' => ['text', 'image', 'voice'],
                'models' => ['gemini-1.5-flash-001', 'gemini-pro-vision'],
                'priority' => 3,
            ],
            self::DEEPSEEK->value => [
                'name' => 'DeepSeek',
                'description' => 'Cost-effective, high-performance NLP tasks',
                'api_endpoint' => 'https://api.deepseek.com/v1',
                'capabilities' => ['text'],
                'models' => ['deepseek-chat', 'deepseek-coder'],
                'priority' => 1,
            ],
            self::ANTHROPIC->value => [
                'name' => 'Anthropic Claude',
                'description' => 'Anthropic Claude for safe and helpful AI assistance',
                'api_endpoint' => 'https://api.anthropic.com/v1',
                'capabilities' => ['text'],
                'models' => ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
                'priority' => 2,
            ],
            self::XAI->value => [
                'name' => 'xAI Grok',
                'description' => 'xAI Grok for advanced reasoning and analysis',
                'api_endpoint' => 'https://api.x.ai/v1',
                'capabilities' => ['text'],
                'models' => ['grok-1'],
                'priority' => 1,
            ],
        ];
    }

    public function detail(): array
    {
        return self::getDetail()[$this->value] ?? [];
    }

    public function getName(): string
    {
        return $this->detail()['name'] ?? $this->value;
    }

    public function getDescription(): string
    {
        return $this->detail()['description'] ?? '';
    }

    public function getApiEndpoint(): string
    {
        return $this->detail()['api_endpoint'] ?? '';
    }

    public function getCapabilities(): array
    {
        return $this->detail()['capabilities'] ?? [];
    }

    public function getModels(): array
    {
        return $this->detail()['models'] ?? [];
    }

    public function getPriority(): int
    {
        return $this->detail()['priority'] ?? 0;
    }
}
