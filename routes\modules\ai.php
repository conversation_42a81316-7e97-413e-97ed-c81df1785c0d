<?php

use App\Http\Controllers\AI\AcademicChatController;
use App\Http\Controllers\AI\AssistantController;
use App\Http\Controllers\AI\ConversationController;
use App\Http\Controllers\AI\DashboardController;
use App\Http\Controllers\AI\KnowledgeBaseController;
use App\Http\Controllers\AI\MessageController;
use App\Http\Controllers\AI\ProviderController;

// Apply global AI check middleware to all AI routes
Route::middleware('check.global.ai.enabled')->group(function () {

// AI Dashboard Routes
Route::prefix('ai/dashboard')->name('ai.dashboard.')->group(function () {
    Route::get('stat', [DashboardController::class, 'stat'])->name('stat');
});

// AI Provider Routes
Route::prefix('ai/providers')->name('ai.providers.')->middleware('permission:ai-provider:read')->group(function () {
    Route::get('pre-requisite', [ProviderController::class, 'preRequisite'])->name('preRequisite');
    Route::apiResource('', ProviderController::class)->parameters(['' => 'provider']);
    
    Route::post('{provider}/api-key', [ProviderController::class, 'updateApiKey'])->name('updateApiKey');
    Route::post('{provider}/test', [ProviderController::class, 'testConnection'])->name('testConnection');
});

// AI Conversation Routes
Route::prefix('ai/conversations')->name('ai.conversations.')->middleware('permission:ai-conversation:read')->group(function () {
    Route::get('pre-requisite', [ConversationController::class, 'preRequisite'])->name('preRequisite');
    Route::apiResource('', ConversationController::class)->parameters(['' => 'conversation']);
    
    // Message routes nested under conversations
    Route::prefix('{conversation}/messages')->name('messages.')->group(function () {
        Route::get('', [MessageController::class, 'index'])->name('index');
        Route::post('', [MessageController::class, 'store'])->name('store');
    });
});

// AI Knowledge Base Routes
Route::prefix('ai/knowledge')->name('ai.knowledge.')->middleware('permission:ai-knowledge:read')->group(function () {
    Route::get('pre-requisite', [KnowledgeBaseController::class, 'preRequisite'])->name('preRequisite');
    Route::get('search', [KnowledgeBaseController::class, 'search'])->name('search');
    Route::apiResource('', KnowledgeBaseController::class)->parameters(['' => 'knowledge']);
    
    Route::post('{knowledge}/approve', [KnowledgeBaseController::class, 'approve'])->name('approve');
});

// AI Assistant Routes
Route::prefix('ai/assistant')->name('ai.assistant.')->middleware('permission:ai:use-assistant')->group(function () {
    Route::post('command', [AssistantController::class, 'processCommand'])->name('command');
    Route::post('action', [AssistantController::class, 'executeAction'])->name('action');
});

// AI Academic Chat Routes
Route::prefix('ai/academic')->name('ai.academic.')->middleware('permission:ai:use-academic-chat')->group(function () {
    Route::post('question', [AcademicChatController::class, 'askQuestion'])->name('question');
    Route::post('lesson-plan', [AcademicChatController::class, 'generateLessonPlan'])->name('lessonPlan');
    Route::post('quiz', [AcademicChatController::class, 'generateQuiz'])->name('quiz');
});

}); // End global AI check middleware group
