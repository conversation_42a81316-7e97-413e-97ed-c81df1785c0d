<?php

namespace App\Services\AI;

use App\Enums\AI\ProviderType;
use App\Models\AI\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProviderService
{
    public function preRequisite(Request $request): array
    {
        $providerTypes = ProviderType::getOptions();
        $capabilities = [
            'text' => 'Text Generation',
            'image' => 'Image Processing',
            'voice' => 'Voice Processing',
            'code' => 'Code Generation',
        ];

        return compact('providerTypes', 'capabilities');
    }

    public function create(Request $request): Provider
    {
        \DB::beginTransaction();

        $provider = Provider::forceCreate($this->formatParams($request));

        // Set default configuration based on provider type
        $this->setDefaultConfig($provider, $request->type);

        \DB::commit();

        return $provider;
    }

    public function update(Request $request, Provider $provider): Provider
    {
        \DB::beginTransaction();

        $provider->forceFill($this->formatParams($request, $provider));
        $provider->save();

        \DB::commit();

        return $provider;
    }

    public function delete(Provider $provider): void
    {
        \DB::beginTransaction();

        // Check if provider is being used
        if ($provider->conversations()->exists()) {
            throw new \Exception('Cannot delete provider that has active conversations.');
        }

        $provider->delete();

        \DB::commit();
    }

    private function formatParams(Request $request, ?Provider $provider = null): array
    {
        $formatted = [
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'api_endpoint' => $request->api_endpoint,
            'is_active' => $request->boolean('is_active', true),
            'is_global' => false, // Tenants can never create global providers
            'priority' => $request->integer('priority', 0),
            'capabilities' => $request->capabilities ?? [],
            'rate_limits' => $request->rate_limits ?? [],
        ];

        if (!$provider) {
            $formatted['slug'] = Str::slug($request->name);
            $formatted['team_id'] = auth()->user()?->current_team_id; // Always set team_id for tenant providers
        }

        return $formatted;
    }

    private function setDefaultConfig(Provider $provider, string $type): void
    {
        $providerType = ProviderType::tryFrom($type);
        if (!$providerType) {
            return;
        }

        $defaultConfig = [
            'api_endpoint' => $providerType->getApiEndpoint(),
            'capabilities' => $providerType->getCapabilities(),
            'models' => $providerType->getModels(),
            'default_model' => $providerType->getModels()[0] ?? null,
            'max_tokens' => 4000,
            'temperature' => 0.7,
        ];

        $provider->setConfig($defaultConfig, true);
        $provider->update(['priority' => $providerType->getPriority()]);
    }

    public function updateApiKey(Provider $provider, string $apiKey): void
    {
        $provider->setApiKey($apiKey);
    }

    public function testConnection(Provider $provider): array
    {
        try {
            $aiService = app(AIService::class);
            $result = $aiService->testProvider($provider);
            
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => $result,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }
}
