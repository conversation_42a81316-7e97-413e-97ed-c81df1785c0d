<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            { label: $trans('ai.ai'), path: 'AIIndex' },
            {
                label: $trans('ai.conversation.conversations'),
                path: 'AIConversations',
            },
        ]"
    >
        <PageHeaderAction
            name="AIConversation"
            :title="$trans('ai.conversation.conversation')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <ShowItem
            :init-url="initUrl"
            :uuid="route.params.uuid"
            @setItem="setItem"
            @redirectTo="router.push({ name: 'AIConversations' })"
        >
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-3" v-if="conversation.uuid">
                <!-- Conversation Details -->
                <div class="lg:col-span-1">
                    <BaseCard>
                        <template #title>
                            {{ $trans('ai.conversation.conversation') }}
                        </template>
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6">
                            <BaseDataView :label="$trans('ai.conversation.props.title')">
                                {{ conversation.title }}
                            </BaseDataView>
                            <BaseDataView :label="$trans('ai.conversation.props.type')">
                                <div class="flex items-center">
                                    <i
                                        :class="conversation.typeIcon"
                                        :style="{ color: conversation.typeColor }"
                                        class="me-2"
                                    ></i>
                                    {{ conversation.typeName }}
                                </div>
                            </BaseDataView>
                            <BaseDataView 
                                v-if="conversation.context"
                                :label="$trans('ai.conversation.props.context')"
                            >
                                <span class="badge bg-secondary">{{ conversation.context }}</span>
                            </BaseDataView>
                            <BaseDataView 
                                v-if="conversation.user"
                                :label="$trans('general.user')"
                            >
                                {{ conversation.user.name }}
                            </BaseDataView>
                            <BaseDataView :label="$trans('general.created_at')">
                                {{ $cal.toUserTimezone(conversation.createdAt.value, 'datetime') }}
                            </BaseDataView>
                            <BaseDataView
                                v-if="conversation.lastMessageAt"
                                :label="$trans('ai.conversation.props.last_message')"
                            >
                                {{ $cal.toUserTimezone(conversation.lastMessageAt.value, 'datetime') }}
                            </BaseDataView>
                            <BaseDataView :label="$trans('general.statistics')">
                                <div class="text-sm">
                                    <div>{{ $trans('ai.message.messages') }}: {{ messages.length }}</div>
                                </div>
                            </BaseDataView>
                        </dl>
                    </BaseCard>
                </div>

                <!-- Messages -->
                <div class="lg:col-span-2">
                    <BaseCard>
                        <template #title>
                            {{ $trans('ai.message.messages') }}
                        </template>
                        <div class="space-y-4" style="max-height: 600px; overflow-y: auto;" ref="messagesContainer">
                            <div v-if="messages.length === 0" class="text-center text-muted py-8">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <h4>{{ $trans('general.no_messages') }}</h4>
                                <p>{{ $trans('ai.conversation.no_messages_description') }}</p>
                            </div>

                            <div v-for="message in messages" :key="message.uuid" class="mb-4">
                                <div :class="['d-flex', message.role === 'user' ? 'justify-content-end' : 'justify-content-start']">
                                    <div 
                                        :class="[
                                            'card border-0 shadow-sm',
                                            message.role === 'user' ? 'bg-primary text-white' : 'bg-light',
                                            message.isError ? 'border-danger' : ''
                                        ]"
                                        style="max-width: 85%;"
                                    >
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-start">
                                                <div v-if="message.role !== 'user'" class="me-2">
                                                    <i :class="getRoleIcon(message.role)"></i>
                                                </div>
                                                <div class="flex-fill">
                                                    <div v-html="formatMessage(message.content)"></div>
                                                    
                                                    <!-- Error indicator -->
                                                    <div v-if="message.isError" class="mt-2">
                                                        <small class="text-danger">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                                            {{ $trans('general.error') }}
                                                        </small>
                                                    </div>

                                                    <!-- Metadata -->
                                                    <div v-if="message.providerUsed || message.tokensUsed" class="mt-2">
                                                        <small :class="message.role === 'user' ? 'text-white-50' : 'text-muted'">
                                                            <span v-if="message.providerUsed">
                                                                {{ $trans('ai.provider.provider') }}: {{ message.providerUsed }}
                                                            </span>
                                                            <span v-if="message.tokensUsed" class="ms-2">
                                                                {{ $trans('ai.tokens') }}: {{ message.tokensUsed }}
                                                            </span>
                                                            <span v-if="message.cost" class="ms-2">
                                                                {{ $trans('ai.cost') }}: ${{ message.cost }}
                                                            </span>
                                                        </small>
                                                    </div>
                                                </div>
                                                <div v-if="message.role === 'user'" class="ms-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </div>
                                            <div class="text-end mt-1">
                                                <small :class="message.role === 'user' ? 'text-white-50' : 'text-muted'">
                                                    {{ $cal.toUserTimezone(message.createdAt.value, 'datetime') }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Loading indicator -->
                            <div v-if="loadingMessages" class="text-center py-4">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">{{ $trans('general.loading') }}</span>
                                </div>
                            </div>
                        </div>
                    </BaseCard>
                </div>
            </div>
        </ShowItem>
    </ParentTransition>
</template>

<script>
export default {
    name: "AIConversationShow",
}
</script>

<script setup>
import { reactive, ref, inject } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import { useToast } from "vue-toastification"

const route = useRoute()
const router = useRouter()
const store = useStore()
const toast = useToast()

const $trans = inject("$trans")
const $cal = inject("$cal")

const initUrl = "ai/conversations/"
const conversation = reactive({})
const messages = ref([])
const loadingMessages = ref(false)
const messagesContainer = ref(null)

const setItem = (data) => {
    Object.assign(conversation, data)
    fetchMessages()
}

const fetchMessages = async () => {
    loadingMessages.value = true
    try {
        const response = await store.dispatch("ai/conversation/fetchMessages", {
            conversationUuid: route.params.uuid,
        })
        messages.value = response.data
    } catch (error) {
        toast.error(error.message || "Failed to fetch messages")
    } finally {
        loadingMessages.value = false
    }
}

const formatMessage = (content) => {
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
}

const getRoleIcon = (role) => {
    const icons = {
        user: "fas fa-user",
        assistant: "fas fa-robot",
        system: "fas fa-cog",
    }
    return icons[role] || "fas fa-comment"
}
</script>
