# AI Module Documentation

## Overview

The AI Module provides comprehensive artificial intelligence integration for multi-tenant applications, supporting multiple AI providers with a two-tier configuration system:

1. **Global Level (App Owner - .env file)**: App owner configures global providers via environment variables
2. **Tenant Level (User Dashboard)**: Individual tenants can optionally add their own providers with personal API keys

## Architecture

### Two-Tier Provider System

#### 1. Global Providers (App Owner Level - Environment Variables)
- **Configured via .env file** by the application owner/administrator
- **Available to all tenants** across the application automatically
- **No tenant UI access** - tenants cannot modify global settings
- **Managed through environment variables** and console commands
- Stored with `is_global = true` and `team_id = null`

#### 2. Tenant-Specific Providers (User Dashboard)
- **Optional additional providers** configured by individual tenants
- **Personal API keys** - tenants use their own credentials
- **Tenant-specific only** - only available to the tenant that created them
- **Managed via AI Providers page** within tenant dashboard
- Stored with `is_global = false` and specific `team_id`

### Provider Priority System

The system uses a priority-based selection mechanism:
1. **Tenant providers** are checked first (higher priority for tenant-specific configurations)
2. **Global providers** are used as fallback when tenant providers are unavailable
3. **Automatic fallback** between providers if one fails (when enabled)

## Supported AI Providers

| Provider | Type | Capabilities | Default Priority |
|----------|------|--------------|------------------|
| DeepSeek | `deepseek` | Text, Code | 1 (Highest) |
| xAI Grok | `xai` | Text | 1 (Highest) |
| OpenAI | `openai` | Text, Image, Voice | 2 |
| Anthropic Claude | `anthropic` | Text | 2 |
| Google Gemini | `gemini` | Text, Image, Voice | 3 |

## Configuration Guide

### Global Configuration (App Owner - .env file)

#### Step 1: Configure Environment Variables
Add comprehensive global AI configuration to your `.env` file:

```bash
# Core AI Settings (Override tenant settings)
AI_ENABLED=true
AI_DEFAULT_PROVIDER=gemini
AI_FALLBACK_ENABLED=true

# Feature Toggles
AI_ENABLE_ASSISTANT=true
AI_ENABLE_ACADEMIC_CHAT=true
AI_ENABLE_VOICE_INPUT=false
AI_ENABLE_VOICE_OUTPUT=false

# Rate Limiting (Applied globally)
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_REQUESTS_PER_MINUTE=60
AI_RATE_LIMIT_REQUESTS_PER_HOUR=1000
AI_RATE_LIMIT_REQUESTS_PER_DAY=10000

# Academic Settings
AI_ACADEMIC_CURRICULUM_FOCUS=nigerian_cambridge
AI_ACADEMIC_DEFAULT_LEVEL=sss

# Data Settings
AI_SAVE_CONVERSATIONS=true
AI_CONVERSATION_RETENTION_DAYS=90
AI_ENABLE_USAGE_ANALYTICS=true
AI_COST_TRACKING_ENABLED=true

# Global AI Provider Configuration
AI_GLOBAL_OPENAI_ENABLED=true
AI_GLOBAL_OPENAI_API_KEY=sk-your-openai-key-here

AI_GLOBAL_GEMINI_ENABLED=true
AI_GLOBAL_GEMINI_API_KEY=your-gemini-api-key-here

AI_GLOBAL_DEEPSEEK_ENABLED=true
AI_GLOBAL_DEEPSEEK_API_KEY=your-deepseek-api-key-here
```

#### Step 2: Verify Global Configuration
Check that your global providers are configured correctly:

```bash
# Check global providers from .env file
php artisan ai:check-global-providers
```

**Important**:
- Global providers are **read directly from .env** (no database records)
- Tenants **cannot modify** global provider settings
- Global providers are **available to all tenants** automatically
- **No database sync needed** - changes to .env take effect immediately

#### Step 3: Configure General Settings
- **Enable AI**: Master switch for AI functionality
- **Enable Assistant**: Enable AI Assistant chat
- **Enable Academic Chat**: Enable Academic AI chat
- **Enable Fallback**: Allow automatic fallback between providers
- **Rate Limiting**: Configure usage limits
- **Conversation Settings**: Configure conversation saving and retention

### Tenant Configuration

#### Step 1: Access Provider Management
1. Navigate to **AI** → **Providers**
2. Ensure you have `ai-provider:create` permission

#### Step 2: Create Tenant Provider
1. Click **Add Provider**
2. Fill in provider details:
   - **Name**: Custom name for the provider
   - **Type**: Select from supported providers
   - **API Key**: Your tenant-specific API key
   - **Priority**: Set priority (higher = preferred)
   - **Capabilities**: Select supported features

#### Step 3: Configure Provider Settings
- **API Endpoint**: Custom endpoint (optional)
- **Rate Limits**: Tenant-specific rate limiting
- **Active Status**: Enable/disable the provider

## Global Provider Management

### Automatic Provider Creation

The system automatically manages global providers based on your configuration:

#### When Global Providers Are Created
- **On Configuration Save**: Every time you save AI configuration
- **Automatic Detection**: System detects enabled providers with API keys
- **Slug Generation**: Creates unique slugs like `global-openai`, `global-gemini`
- **Default Settings**: Applies provider-specific defaults (endpoints, models, capabilities)

#### Provider Lifecycle
```php
// Provider creation flow
if (enable_global_openai && global_openai_api_key) {
    // Create or update global OpenAI provider
    Provider::create([
        'name' => 'Global OpenAI',
        'slug' => 'global-openai',
        'type' => 'openai',
        'is_global' => true,
        'team_id' => null,
        'is_active' => true,
        // ... other settings
    ]);
}
```

#### Configuration Sync
- **Enable + API Key**: Creates active global provider
- **Disable**: Deactivates existing global provider (preserves data)
- **Update API Key**: Updates existing provider with new credentials
- **Remove API Key**: Deactivates provider but keeps configuration

## How Provider Selection Works

### Selection Algorithm

```php
// Simplified provider selection logic
function getAvailableProvider($teamId, $excludeIds = []) {
    return Provider::query()
        ->where(function($q) use ($teamId) {
            $q->where('team_id', $teamId)      // Tenant providers
              ->orWhere('is_global', true);     // Global providers
        })
        ->where('is_active', true)
        ->whereNotIn('id', $excludeIds)
        ->orderBy('priority', 'desc')           // Higher priority first
        ->orderBy('team_id', 'desc')            // Tenant providers first
        ->first();
}
```

### Priority Rules
1. **Tenant providers** always take precedence over global providers
2. Within the same level, **higher priority** values are preferred
3. **Fallback mechanism** tries next available provider on failure

## Features

### Chat Interfaces

#### AI Assistant
- General-purpose AI assistant
- Supports all provider capabilities
- Conversation history and persistence
- Voice input/output (when enabled)

#### Academic Chat
- Educational content generation
- Curriculum-specific responses
- Subject and level filtering
- Lesson plan and quiz generation

### Conversation Management
- **Automatic Saving**: Conversations saved by default
- **History Access**: View and continue previous conversations
- **Retention Policy**: Configurable retention period
- **Export Options**: Export conversation data

### Usage Analytics
- **Cost Tracking**: Monitor API usage costs
- **Usage Logs**: Detailed usage statistics
- **Rate Limiting**: Prevent abuse and control costs
- **Analytics Dashboard**: Visual usage reports

## API Integration

### Provider Classes

Each AI provider implements the `BaseProvider` interface:

```php
abstract class BaseProvider {
    abstract public function sendMessage(string $message, array $context, Provider $provider): array;
    protected function formatMessages(array $context): array;
    protected function calculateCost(array $response): float;
}
```

### Example Provider Implementation

```php
class OpenAIProvider extends BaseProvider {
    public function sendMessage(string $message, array $context, Provider $provider): array {
        $apiKey = $provider->getApiKey();
        $model = $provider->getConfig('default_model', 'gpt-3.5-turbo');
        
        // API call implementation
        return [
            'content' => $response,
            'model' => $model,
            'tokens' => $tokenCount,
            'cost' => $this->calculateCost($response)
        ];
    }
}
```

## Security Considerations

### API Key Management
- **Encryption**: API keys are encrypted in database
- **No Exposure**: Keys never returned in API responses
- **Secure Input**: Password fields for key entry
- **Rotation**: Easy key rotation through UI

### Access Control
- **Permission-based**: Fine-grained permissions for AI features
- **Tenant Isolation**: Strict tenant data separation
- **Global Permissions**: Special permissions for global configuration

### Rate Limiting
- **Multiple Levels**: Per-minute, per-hour, per-day limits
- **Tenant-specific**: Individual tenant rate limits
- **Global Limits**: Application-wide rate limiting
- **Cost Protection**: Prevent unexpected charges

## Troubleshooting

### Common Issues

#### No Providers Available
**Symptoms**: "No AI providers available" error
**Solutions**:
1. Check if AI is enabled in configuration
2. Verify at least one provider is active
3. Ensure API keys are correctly configured
4. Check provider priority settings

#### API Key Errors
**Symptoms**: "API key not configured" error
**Solutions**:
1. Verify API key is entered correctly
2. Check provider is enabled
3. Ensure API key has sufficient permissions
4. Test with provider's official tools

#### Rate Limit Exceeded
**Symptoms**: "Rate limit exceeded" error
**Solutions**:
1. Check rate limit configuration
2. Increase limits if appropriate
3. Implement usage monitoring
4. Consider upgrading provider plan

### Debugging

#### Enable Debug Logging
```php
// In .env file
LOG_LEVEL=debug
AI_DEBUG=true
```

#### Check Usage Logs
Navigate to **AI** → **Usage Logs** to view detailed API call logs.

#### Monitor Provider Status
Check **AI** → **Providers** for provider health and status.

## Best Practices

### For App Owners
1. **Start with Global Providers**: Configure reliable global providers first
2. **Monitor Usage**: Set up usage alerts and monitoring
3. **Cost Management**: Implement appropriate rate limits
4. **Backup Providers**: Configure multiple providers for redundancy

### For Tenants
1. **Test Thoroughly**: Test your API keys before production use
2. **Monitor Costs**: Track usage to avoid unexpected charges
3. **Optimize Prompts**: Use efficient prompts to reduce token usage
4. **Backup Strategy**: Consider multiple providers for critical applications

### Security Best Practices
1. **Rotate Keys Regularly**: Change API keys periodically
2. **Limit Permissions**: Use API keys with minimal required permissions
3. **Monitor Access**: Review usage logs regularly
4. **Secure Storage**: Never store API keys in code or logs

## Migration and Maintenance

### Upgrading Providers
1. Test new provider versions in development
2. Update configuration gradually
3. Monitor performance after changes
4. Keep fallback providers active during transitions

### Data Migration
- Conversation data is preserved during provider changes
- Usage logs maintain historical data
- Configuration changes are versioned

## Quick Setup Guide

### For App Owners (Global Setup)

#### 1. Configure Environment Variables
```bash
# Add to your .env file
AI_GLOBAL_OPENAI_ENABLED=true
AI_GLOBAL_OPENAI_API_KEY=sk-your-openai-key-here
```

#### 2. Sync Global Providers
```bash
# Create global providers from .env
php artisan ai:sync-global-providers
```

#### 3. Enable AI Features
Navigate to **Configuration** → **AI Configuration** and enable:
- Enable AI: `true`
- Enable Assistant: `true`
- Enable Academic Chat: `true`
- Save Conversations: `true`

#### 4. Test the Setup
1. Navigate to AI → Assistant
2. Send a test message (will use global provider)
3. Check AI → Usage Logs for successful API calls

### For Tenants (Optional Custom Setup)

#### 1. Check Available Providers
1. Navigate to **AI** → **Providers**
2. You should see global providers (configured by app owner)
3. Global providers are **read-only** and cannot be modified

#### 2. Add Your Own Provider (Optional)
If you want to use your own API keys:
1. Click **Add Provider**
2. Configure your provider:
   - **Name**: "My Personal OpenAI"
   - **Type**: "openai"
   - **API Key**: "sk-your-personal-key"
   - **Priority**: 5 (higher than global providers)

#### 3. Test Your Setup
1. Go to **AI** → **Assistant** or **AI** → **Academic**
2. Send test messages (system will prefer your provider over global)
3. Monitor usage in **AI** → **Usage Logs**

## Advanced Configuration

### Environment Variables
```bash
# Optional: Set in .env for additional control
AI_DEBUG=true                    # Enable debug logging
AI_DEFAULT_PROVIDER=openai       # Set default provider
AI_RATE_LIMIT_ENABLED=true      # Enable rate limiting
AI_COST_TRACKING=true           # Enable cost tracking
```

### Database Configuration
```sql
-- Check global providers
SELECT name, type, is_global, is_active FROM ai_providers WHERE is_global = 1;

-- Check tenant providers
SELECT name, type, team_id, is_active FROM ai_providers WHERE team_id = YOUR_TEAM_ID;

-- View recent usage
SELECT * FROM ai_usage_logs ORDER BY created_at DESC LIMIT 10;
```

### API Testing
```bash
# Test provider connectivity
curl -X POST "https://your-app.com/api/ai/test" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, AI!", "provider": "openai"}'
```

## Monitoring and Maintenance

### Health Checks
- **Provider Status**: Check AI → Providers for active/inactive status
- **Usage Monitoring**: Review AI → Usage Logs for errors
- **Cost Tracking**: Monitor AI → Analytics for usage costs
- **Rate Limits**: Check for rate limit violations in logs

### Regular Maintenance
1. **Weekly**: Review usage logs and costs
2. **Monthly**: Rotate API keys for security
3. **Quarterly**: Review and optimize provider priorities
4. **Annually**: Audit global vs tenant provider usage

### Performance Optimization
- **Provider Priorities**: Set faster/cheaper providers with higher priority
- **Rate Limits**: Adjust based on actual usage patterns
- **Fallback Strategy**: Ensure multiple providers for redundancy
- **Token Optimization**: Monitor token usage and optimize prompts

## Support and Resources

### Documentation
- Provider-specific documentation in `/docs/providers/`
- API reference in `/docs/api/`
- Configuration examples in `/docs/examples/`

### Community
- GitHub Issues for bug reports
- Discussion forum for questions
- Community plugins and extensions

### Getting Help
1. **Check Logs**: Always check AI → Usage Logs first
2. **Test Providers**: Use AI → Providers to test individual providers
3. **Configuration**: Verify AI Configuration settings
4. **Permissions**: Ensure proper user permissions are set

---

*This documentation covers the core AI module functionality. The system is designed to work out-of-the-box with minimal configuration while providing extensive customization options for advanced users.*
