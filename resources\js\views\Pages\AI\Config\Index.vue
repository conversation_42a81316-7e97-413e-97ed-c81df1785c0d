<template>
    <div>
        <PageHeader
            :title="$trans('ai.config.config')"
            :navs="[
                { label: $trans('ai.ai'), path: 'AIIndex' },
            ]"
        />

        <FormAction
            :init-url="initUrl"
            data-fetch="ai"
            :init-form="initForm"
            :form="form"
            :set-form="setForm"
            action="store"
            :keep-adding="false"
            :stay-on="true"
        >

        <div v-if="form && form.hasOwnProperty('enableAi')" class="space-y-6">
            <!-- General Settings -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.config.general_settings')"
                        icon="fas fa-cog"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <BaseSwitch
                        v-model="form.enableAi"
                        name="enableAi"
                        :label="$trans('ai.config.props.enable_ai')"
                        v-model:error="formErrors.enableAi"
                    />
                    <BaseSwitch
                        v-model="form.enableAssistant"
                        name="enableAssistant"
                        :label="$trans('ai.config.props.enable_assistant')"
                        v-model:error="formErrors.enableAssistant"
                    />
                    <BaseSwitch
                        v-model="form.enableAcademicChat"
                        name="enableAcademicChat"
                        :label="$trans('ai.config.props.enable_academic_chat')"
                        v-model:error="formErrors.enableAcademicChat"
                    />
                    <BaseSwitch
                        v-model="form.enableVoiceInput"
                        name="enableVoiceInput"
                        :label="$trans('ai.config.props.enable_voice_input')"
                        v-model:error="formErrors.enableVoiceInput"
                    />
                    <BaseSwitch
                        v-model="form.enableVoiceOutput"
                        name="enableVoiceOutput"
                        :label="$trans('ai.config.props.enable_voice_output')"
                        v-model:error="formErrors.enableVoiceOutput"
                    />
                    <BaseSwitch
                        v-model="form.enableFallback"
                        name="enableFallback"
                        :label="$trans('ai.config.props.enable_fallback')"
                        v-model:error="formErrors.enableFallback"
                    />
                </div>
            </BaseCard>

            <!-- Provider Settings -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.config.provider_settings')"
                        icon="fas fa-robot"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <BaseInput
                        type="text"
                        v-model="form.defaultProvider"
                        name="defaultProvider"
                        :label="$trans('ai.config.props.default_provider')"
                        v-model:error="formErrors.defaultProvider"
                    />
                    <BaseInput
                        type="number"
                        v-model="form.maxTokensPerRequest"
                        name="maxTokensPerRequest"
                        :label="$trans('ai.config.props.max_tokens_per_request')"
                        v-model:error="formErrors.maxTokensPerRequest"
                        min="100"
                        max="10000"
                    />
                    <BaseInput
                        type="number"
                        v-model="form.maxConversationLength"
                        name="maxConversationLength"
                        :label="$trans('ai.config.props.max_conversation_length')"
                        v-model:error="formErrors.maxConversationLength"
                        min="10"
                        max="100"
                    />
                    <BaseSwitch
                        v-model="form.enableContentModeration"
                        name="enableContentModeration"
                        :label="$trans('ai.config.props.enable_content_moderation')"
                        v-model:error="formErrors.enableContentModeration"
                    />
                </div>
            </BaseCard>

            <!-- Rate Limiting -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.config.rate_limiting')"
                        icon="fas fa-tachometer-alt"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6">
                    <BaseSwitch
                        v-model="form.enableRateLimit"
                        name="enableRateLimit"
                        :label="$trans('ai.config.props.enable_rate_limit')"
                        v-model:error="formErrors.enableRateLimit"
                    />

                    <div v-if="form.enableRateLimit" class="grid grid-cols-1 gap-6 sm:grid-cols-3">
                        <BaseInput
                            type="number"
                            v-model="form.rateLimitRequestsPerMinute"
                            name="rateLimitRequestsPerMinute"
                            :label="$trans('ai.config.props.rate_limit_requests_per_minute')"
                            v-model:error="formErrors.rateLimitRequestsPerMinute"
                            min="1"
                            max="1000"
                        />
                        <BaseInput
                            type="number"
                            v-model="form.rateLimitRequestsPerHour"
                            name="rateLimitRequestsPerHour"
                            :label="$trans('ai.config.props.rate_limit_requests_per_hour')"
                            v-model:error="formErrors.rateLimitRequestsPerHour"
                            min="1"
                            max="10000"
                        />
                        <BaseInput
                            type="number"
                            v-model="form.rateLimitRequestsPerDay"
                            name="rateLimitRequestsPerDay"
                            :label="$trans('ai.config.props.rate_limit_requests_per_day')"
                            v-model:error="formErrors.rateLimitRequestsPerDay"
                            min="1"
                            max="100000"
                        />
                    </div>
                </div>
            </BaseCard>

            <!-- Academic Settings -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.config.academic_settings')"
                        icon="fas fa-graduation-cap"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <BaseSelect
                        v-model="form.academicCurriculumFocus"
                        name="academicCurriculumFocus"
                        :options="curriculumOptions"
                        :label="$trans('ai.config.props.academic_curriculum_focus')"
                        v-model:error="formErrors.academicCurriculumFocus"
                    />
                    <BaseSelect
                        v-model="form.academicDefaultLevel"
                        name="academicDefaultLevel"
                        :options="levelOptions"
                        :label="$trans('ai.config.props.academic_default_level')"
                        v-model:error="formErrors.academicDefaultLevel"
                    />
                </div>
            </BaseCard>

            <!-- Knowledge Base Settings -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.knowledge.knowledge_base')"
                        icon="fas fa-database"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <BaseSwitch
                        v-model="form.autoApproveKnowledge"
                        name="autoApproveKnowledge"
                        :label="$trans('ai.config.props.auto_approve_knowledge')"
                        v-model:error="formErrors.autoApproveKnowledge"
                    />
                    <BaseSwitch
                        v-model="form.isKnowledgePublicByDefault"
                        name="isKnowledgePublicByDefault"
                        :label="$trans('ai.config.props.is_knowledge_public_by_default')"
                        v-model:error="formErrors.isKnowledgePublicByDefault"
                    />
                </div>
            </BaseCard>

            <!-- Data & Analytics -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('general.data_analytics')"
                        icon="fas fa-chart-bar"
                    />
                </template>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <BaseSwitch
                        v-model="form.enableConversationSaving"
                        name="enableConversationSaving"
                        :label="$trans('ai.config.props.enable_conversation_saving')"
                        v-model:error="formErrors.enableConversationSaving"
                    />
                    <BaseInput
                        type="number"
                        v-model="form.conversationRetentionDays"
                        name="conversationRetentionDays"
                        :label="$trans('ai.config.props.conversation_retention_days')"
                        v-model:error="formErrors.conversationRetentionDays"
                        min="1"
                        max="365"
                    />
                    <BaseSwitch
                        v-model="form.enableUsageAnalytics"
                        name="enableUsageAnalytics"
                        :label="$trans('ai.config.props.enable_usage_analytics')"
                        v-model:error="formErrors.enableUsageAnalytics"
                    />
                    <BaseSwitch
                        v-model="form.enableCostTracking"
                        name="enableCostTracking"
                        :label="$trans('ai.config.props.enable_cost_tracking')"
                        v-model:error="formErrors.enableCostTracking"
                    />
                </div>
            </BaseCard>

            <!-- Global Configuration Information -->
            <BaseCard>
                <template #header>
                    <CardHeader
                        :title="$trans('ai.config.global_config_info')"
                        icon="fas fa-server"
                    />
                </template>

                <div class="space-y-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ $trans('ai.config.global_config_info_text') }}
                    </div>

                    <div class="alert alert-warning" v-if="globalOverrides.length > 0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>{{ $trans('ai.config.global_overrides_active') }}</strong>
                        <ul class="mt-2 mb-0">
                            <li v-for="override in globalOverrides" :key="override.key">
                                <code>{{ override.key }}</code>: {{ override.value }}
                            </li>
                        </ul>
                        <small class="text-muted d-block mt-2">
                            {{ $trans('ai.config.global_overrides_note') }}
                        </small>
                    </div>
                </div>
            </BaseCard>
        </div>
        </FormAction>
    </div>
</template>

<script>
export default {
    name: "AIConfig",
}
</script>

<script setup>
import { reactive, inject } from "vue"
import { cloneDeep } from "@core/utils"
import { getFormErrors, perform } from "@core/helpers/action"

const $trans = inject("$trans")

const initUrl = "config/"
const formErrors = getFormErrors(initUrl)

const initForm = {
    enableAi: false,
    enableAssistant: true,
    enableAcademicChat: true,
    enableVoiceInput: false,
    enableVoiceOutput: false,
    defaultProvider: "",
    enableFallback: true,
    enableRateLimit: false,
    rateLimitRequestsPerMinute: 60,
    rateLimitRequestsPerHour: 1000,
    rateLimitRequestsPerDay: 10000,
    autoApproveKnowledge: false,
    isKnowledgePublicByDefault: false,
    enableConversationSaving: true,
    conversationRetentionDays: 90,
    enableUsageAnalytics: true,
    enableCostTracking: true,
    maxTokensPerRequest: 4000,
    maxConversationLength: 50,
    enableContentModeration: false,
    academicCurriculumFocus: "nigerian_cambridge",
    academicDefaultLevel: "sss",
    assistantContextModules: [],

    type: "ai",
}

const form = reactive({ ...initForm })
const globalOverrides = reactive([])

const curriculumOptions = [
    { label: $trans("ai.categories.nigerian_cambridge"), value: "nigerian_cambridge" },
    { label: $trans("ai.categories.waec"), value: "waec" },
    { label: $trans("ai.categories.igcse"), value: "igcse" },
    { label: $trans("ai.categories.a_levels"), value: "a_levels" },
    { label: $trans("ai.categories.nigerian_national"), value: "nigerian_national" },
    { label: $trans("ai.categories.british_curriculum"), value: "british_curriculum" },
]

const levelOptions = [
    { label: $trans("ai.levels.primary"), value: "primary" },
    { label: $trans("ai.levels.jss"), value: "jss" },
    { label: $trans("ai.levels.sss"), value: "sss" },
    { label: $trans("ai.levels.university"), value: "university" },
]

const setForm = (data) => {
    Object.assign(initForm, data)
    Object.assign(form, cloneDeep(initForm))
}
</script>
