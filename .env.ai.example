# AI Module Global Configuration
# Copy these settings to your .env file and configure as needed

# ============================================================================
# GLOBAL AI CONFIGURATION
# These settings override tenant-specific configurations and apply globally
# ============================================================================

# Core AI Settings
AI_ENABLED=true
AI_DEFAULT_PROVIDER=gemini
AI_FALLBACK_ENABLED=true

# Feature Toggles
AI_ENABLE_ASSISTANT=true
AI_ENABLE_ACADEMIC_CHAT=true
AI_ENABLE_VOICE_INPUT=false
AI_ENABLE_VOICE_OUTPUT=false
AI_ENABLE_CONTENT_MODERATION=false

# Rate Limiting
AI_RATE_LIMIT_ENABLED=true
AI_RATE_LIMIT_REQUESTS_PER_MINUTE=60
AI_RATE_LIMIT_REQUESTS_PER_HOUR=1000
AI_RATE_LIMIT_REQUESTS_PER_DAY=10000

# Academic Settings
AI_ACADEMIC_CURRICULUM_FOCUS=nigerian_cambridge
AI_ACADEMIC_DEFAULT_LEVEL=sss

# Data Settings
AI_SAVE_CONVERSATIONS=true
AI_CONVERSATION_RETENTION_DAYS=90
AI_ENABLE_USAGE_ANALYTICS=true
AI_COST_TRACKING_ENABLED=true

# Token and Request Limits
AI_MAX_TOKENS_PER_REQUEST=4000
AI_MAX_CONVERSATION_LENGTH=50

# ============================================================================
# GLOBAL AI PROVIDERS
# These providers will be available to all tenants in your application
# ============================================================================

# OpenAI Configuration
AI_GLOBAL_OPENAI_ENABLED=false
AI_GLOBAL_OPENAI_API_KEY=

# Google Gemini Configuration  
AI_GLOBAL_GEMINI_ENABLED=false
AI_GLOBAL_GEMINI_API_KEY=

# DeepSeek Configuration
AI_GLOBAL_DEEPSEEK_ENABLED=false
AI_GLOBAL_DEEPSEEK_API_KEY=

# Anthropic Claude Configuration
AI_GLOBAL_ANTHROPIC_ENABLED=false
AI_GLOBAL_ANTHROPIC_API_KEY=

# xAI Grok Configuration
AI_GLOBAL_XAI_ENABLED=false
AI_GLOBAL_XAI_API_KEY=

# ============================================================================
# EXAMPLE CONFIGURATION
# ============================================================================

# Example: Enable OpenAI as global provider
# AI_GLOBAL_OPENAI_ENABLED=true
# AI_GLOBAL_OPENAI_API_KEY=sk-your-openai-api-key-here

# Example: Enable multiple providers
# AI_GLOBAL_OPENAI_ENABLED=true
# AI_GLOBAL_OPENAI_API_KEY=sk-your-openai-key
# AI_GLOBAL_GEMINI_ENABLED=true  
# AI_GLOBAL_GEMINI_API_KEY=your-gemini-key
# AI_GLOBAL_DEEPSEEK_ENABLED=true
# AI_GLOBAL_DEEPSEEK_API_KEY=your-deepseek-key

# ============================================================================
# SETUP INSTRUCTIONS
# ============================================================================

# 1. Copy the desired configuration lines to your .env file
# 2. Set ENABLED=true for providers you want to activate
# 3. Add your API keys (keep them secure!)
# 4. Run: php artisan ai:sync-global-providers
# 5. Global providers will be available to all tenants

# ============================================================================
# SECURITY NOTES
# ============================================================================

# - Keep your .env file secure and never commit it to version control
# - API keys should be kept confidential
# - Global providers use YOUR API keys and billing
# - Monitor usage through AI → Usage Logs
# - Set up rate limiting to control costs

# ============================================================================
# PROVIDER PRIORITIES (Default)
# ============================================================================

# DeepSeek: Priority 1 (Highest - Cost effective)
# xAI: Priority 1 (Highest - Advanced reasoning)  
# OpenAI: Priority 2 (High - Versatile)
# Anthropic: Priority 2 (High - Safe AI)
# Gemini: Priority 3 (Medium - Google's AI)

# Tenants can add their own providers with higher priorities
# to override global providers for their specific needs
