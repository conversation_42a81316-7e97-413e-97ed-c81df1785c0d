<template>
    <ListItem
        :init-url="initUrl"
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        @setItems="setItems"
    >
        <template #header>
            <PageHeader
                :title="$trans('ai.knowledge.knowledge_base')"
                :navs="[
                    { label: $trans('ai.ai'), path: 'AIIndex' },
                ]"
            >
                <PageHeaderAction
                    url="ai/knowledge/"
                    name="AIKnowledge"
                    :title="$trans('ai.knowledge.knowledge_base')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <Filter
                    @hide="showFilter = false"
                />
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="knowledgeItems.headers"
                :meta="knowledgeItems.meta"
                module="ai.knowledge"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="knowledge in knowledgeItems.data"
                    :key="knowledge.uuid"
                    @double-click="
                        router.push({
                            name: 'AIKnowledgeShow',
                            params: { uuid: knowledge.uuid },
                        })
                    "
                >
                    <DataCell name="title" clickable>
                        <div class="flex items-center">
                            <div class="me-3">
                                <i 
                                    :class="knowledge.type_icon" 
                                    :style="{ color: knowledge.type_color }"
                                ></i>
                            </div>
                            <div>
                                <div class="font-medium">{{ knowledge.title }}</div>
                                <div class="text-muted small">{{ knowledge.type_name }}</div>
                            </div>
                        </div>
                    </DataCell>
                    <DataCell name="category">
                        <span v-if="knowledge.category" class="badge bg-primary">
                            {{ knowledge.category }}
                        </span>
                        <span v-else class="text-muted">-</span>
                    </DataCell>
                    <DataCell name="subject">
                        <span v-if="knowledge.subject" class="badge bg-info">
                            {{ knowledge.subject }}
                        </span>
                        <span v-else class="text-muted">-</span>
                    </DataCell>
                    <DataCell name="level">
                        <span v-if="knowledge.level" class="badge bg-secondary">
                            {{ knowledge.level }}
                        </span>
                        <span v-else class="text-muted">-</span>
                    </DataCell>
                    <DataCell name="user">
                        <div v-if="knowledge.user">
                            {{ knowledge.user.name }}
                        </div>
                        <span v-else class="text-muted">-</span>
                    </DataCell>
                    <DataCell name="status">
                        <div class="d-flex align-items-center gap-1">
                            <span 
                                :class="[
                                    'badge',
                                    knowledge.is_approved ? 'bg-success' : 'bg-warning'
                                ]"
                            >
                                {{ knowledge.is_approved ? $trans('general.approved') : $trans('general.pending') }}
                            </span>
                            <span 
                                v-if="knowledge.is_public"
                                class="badge bg-info"
                            >
                                {{ $trans('general.public') }}
                            </span>
                        </div>
                    </DataCell>
                    <DataCell name="created_at">
                        {{ moment(knowledge.created_at).format('MMM DD, YYYY') }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                v-if="perform('ai-knowledge:read')"
                                icon="fas fa-arrow-circle-right"
                                @click="
                                    router.push({
                                        name: 'AIKnowledgeShow',
                                        params: { uuid: knowledge.uuid },
                                    })
                                "
                                >{{ $trans('general.show') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-knowledge:edit')"
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'AIKnowledgeEdit',
                                        params: { uuid: knowledge.uuid },
                                    })
                                "
                                >{{ $trans('general.edit') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-knowledge:approve') && !knowledge.is_approved"
                                icon="fas fa-check"
                                @click="approveKnowledge(knowledge)"
                                >{{ $trans('ai.knowledge.approve_knowledge') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-knowledge:delete')"
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: knowledge.uuid,
                                    })
                                "
                                >{{ $trans('general.delete') }}</FloatingMenuItem
                            >
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "AIKnowledgeIndex",
}
</script>

<script setup>
import { reactive, ref, inject } from "vue"
import { useRouter } from "vue-router"
import { useStore } from "vuex"
import { useToast } from "vue-toastification"
import { perform } from '@core/helpers/action'
import Filter from './Filter.vue'

const router = useRouter()
const store = useStore()
const toast = useToast()
const $trans = inject('$trans')
const emitter = inject('emitter')
const moment = inject('moment')

const initUrl = "ai/knowledge/"
const showFilter = ref(false)

const preRequisites = reactive({
    knowledgeTypes: [],
    categories: [],
    subjects: [],
    levels: [],
})

const knowledgeItems = reactive({
    data: [],
    headers: [],
    meta: {},
})

let userActions = ["filter"]
if (perform("ai-knowledge:create")) {
    userActions.unshift("create")
}

let dropdownActions = []
if (perform("ai-knowledge:export")) {
    dropdownActions = ["print", "pdf", "excel"]
}

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const setItems = (data) => {
    Object.assign(knowledgeItems, data)
}

const approveKnowledge = async (knowledge) => {
    try {
        await store.dispatch("ai/knowledge/approve", {
            uuid: knowledge.uuid,
        })
        // Success toast is already shown by the store action
        emitter.emit("listItems")
    } catch (error) {
        toast.error(error.message || "Failed to approve knowledge")
    }
}



</script>
