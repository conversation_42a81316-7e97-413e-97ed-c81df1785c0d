<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :init-form="initForm"
        :form="form"
        :set-form="setForm"
        redirect="ExamOnlineExam"
        @resetMediaFiles="resetMediaFiles"
    >
        <div class="grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-3">
                <BaseInput
                    type="text"
                    v-model="form.title"
                    name="title"
                    :label="$trans('exam.online_exam.props.title')"
                    v-model:error="formErrors.title"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.online_exam.props.type')"
                    :options="preRequisites.types"
                    v-model:error="formErrors.type"
                />
            </div>
            <div class="col-span-4">
                <BaseSwitch
                    vertical
                    v-model="form.isFlexibleTiming"
                    name="isFlexibleTiming"
                    :label="$trans('exam.online_exam.flexible_timing')"
                    :description="$trans('exam.online_exam.flexible_timing_help')"
                    v-model:error="formErrors.isFlexibleTiming"
                />
            </div>
            <!-- Traditional Timing Fields -->
            <template v-if="!form.isFlexibleTiming">
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.date"
                        name="date"
                        :label="$trans('exam.online_exam.props.date')"
                        no-clear
                        v-model:error="formErrors.date"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.startTime"
                        name="startTime"
                        :label="$trans('exam.online_exam.props.start_time')"
                        as="time"
                        v-model:error="formErrors.startTime"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.endDate"
                        name="endDate"
                        :label="$trans('exam.online_exam.props.end_date')"
                        no-clear
                        v-model:error="formErrors.endDate"
                    />
                    <HelperText>{{
                        $trans("exam.online_exam.end_date_info")
                    }}</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.endTime"
                        name="endTime"
                        :label="$trans('exam.online_exam.props.end_time')"
                        as="time"
                        v-model:error="formErrors.endTime"
                    />
                </div>
            </template>

            <!-- Flexible Timing Fields -->
            <template v-if="form.isFlexibleTiming">
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.date"
                        name="date"
                        :label="$trans('exam.online_exam.props.date')"
                        no-clear
                        v-model:error="formErrors.date"
                    />
                    <HelperText>Start Date</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.startTime"
                        name="startTime"
                        :label="$trans('exam.online_exam.props.start_time')"
                        as="time"
                        v-model:error="formErrors.startTime"
                    />
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <BaseInput
                        type="number"
                        v-model="form.durationMinutes"
                        name="durationMinutes"
                        :label="$trans('exam.online_exam.duration_minutes')"
                        v-model:error="formErrors.durationMinutes"
                        min="1"
                        max="480"
                    />
                    <HelperText>Maximum Duration In Minutes</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.expiryDate"
                        name="expiryDate"
                        :label="$trans('exam.online_exam.expiry_date')"
                        v-model:error="formErrors.expiryDate"
                    />
                    <HelperText>Optional: Leave blank for no expiry</HelperText>
                </div>
                <div class="col-span-4 sm:col-span-1">
                    <DatePicker
                        v-model="form.expiryTime"
                        name="expiryTime"
                        :label="$trans('exam.online_exam.expiry_time')"
                        as="time"
                        v-model:error="formErrors.expiryTime"
                    />
                    <HelperText>Optional: Defaults to 23:59:59</HelperText>
                </div>
                <div class="col-span-4">
                    <BaseSwitch
                        vertical
                        v-model="form.autoPublishResultsForFlexibleTiming"
                        name="autoPublishResultsForFlexibleTiming"
                        :label="$trans('exam.online_exam.auto_publish_results')" 
                        :description="$trans('exam.online_exam.auto_publish_results_help')"
                        v-model:error="formErrors.autoPublishResultsForFlexibleTiming"
                    />
                </div>
            </template>
        </div>
        <div class="mt-4 grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    multiple
                    name="batches"
                    :label="$trans('academic.batch.batch')"
                    v-model="form.batches"
                    v-model:error="formErrors.batches"
                    value-prop="uuid"
                    :init-search="fetchData.batches"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }} -
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }} -
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSelect
                    v-model="form.subject"
                    name="subject"
                    :label="$trans('academic.subject.subject')"
                    label-prop="name"
                    value-prop="uuid"
                    :options="preRequisites.subjects"
                    v-model:error="formErrors.subject"
                />
            </div>
        </div>
        <div class="mt-4 grid grid-cols-4 gap-6">
            <div class="col-span-4 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.passPercentage"
                    name="passPercentage"
                    :label="$trans('exam.online_exam.props.pass_percentage')"
                    v-model:error="formErrors.passPercentage"
                />
            </div>
            <div class="col-span-4 sm:col-span-1">
                <BaseSwitch
                    vertical
                    v-model="form.hasNegativeMarking"
                    name="hasNegativeMarking"
                    :label="
                        $trans('exam.online_exam.props.has_negative_marking')
                    "
                    v-model:error="formErrors.hasNegativeMarking"
                />
            </div>
            <div
                class="col-span-4 sm:col-span-1"
                v-if="form.hasNegativeMarking"
            >
                <BaseInput
                    type="number"
                    v-model="form.negativeMarkPercentPerQuestion"
                    name="negativeMarkPercentPerQuestion"
                    :label="
                        $trans(
                            'exam.online_exam.props.negative_mark_percent_per_question'
                        )
                    "
                    v-model:error="formErrors.negativeMarkPercentPerQuestion"
                />
            </div>
            <div class="col-span-4">
                <BaseEditor
                    v-model="form.instructions"
                    name="instructions"
                    :edit="route.params.uuid ? true : false"
                    :label="$trans('exam.online_exam.props.instructions')"
                    v-model:error="formErrors.instructions"
                />
            </div>
            <div class="col-span-4">
                <BaseEditor
                    v-model="form.description"
                    name="description"
                    :edit="route.params.uuid ? true : false"
                    :label="$trans('exam.online_exam.props.description')"
                    v-model:error="formErrors.description"
                />
            </div>
        </div>
        <div class="mt-4 grid grid-cols-1">
            <div class="col">
                <MediaUpload
                    multiple
                    :label="$trans('general.file')"
                    module="online_exam"
                    :media="form.media"
                    :media-token="form.mediaToken"
                    @isUpdated="form.mediaUpdated = true"
                    @setHash="(hash) => form.mediaHash.push(hash)"
                />
            </div>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "ExamOnlineExamForm",
}
</script>

<script setup>
import { reactive } from "vue"
import { useRoute } from "vue-router"
import { cloneDeep } from "@core/utils"
import { getFormErrors } from "@core/helpers/action"
import { v4 as uuidv4 } from "uuid"

const route = useRoute()

const initForm = {
    title: "",
    type: "",
    isFlexibleTiming: false,
    date: "",
    startTime: "",
    endDate: "",
    endTime: "",
    durationMinutes: null,
    expiryDate: "",
    expiryTime: "",
    autoPublishResultsForFlexibleTiming: true,
    batches: [],
    subject: "",
    passPercentage: 0,
    hasNegativeMarking: false,
    negativeMarkPercentPerQuestion: 0,
    instructions: "",
    description: "",
    media: [],
    mediaUpdated: false,
    mediaToken: uuidv4(),
    mediaHash: [],
}

const initUrl = "exam/onlineExam/"
const formErrors = getFormErrors(initUrl)
const preRequisites = reactive({
    types: [],
    subjects: [],
})

const form = reactive({ ...initForm })
const fetchData = reactive({
    batches: [],
    subject: "",
    isLoaded: route.params.uuid ? false : true,
})

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const resetMediaFiles = () => {
    form.mediaToken = uuidv4()
    form.mediaHash = []
}

const setForm = (data) => {
    let batches =
        data.records.map((record) => {
            return record.batch.uuid
        }) || []

    Object.assign(initForm, {
        ...data,
        type: data.type.value,
        isFlexibleTiming: data.isFlexibleTiming || false,
        date: data.date.value,
        startTime: data.startTime.at,
        endDate: data.endDate.value,
        endTime: data.endTime.at,
        durationMinutes: data.durationMinutes,
        expiryDate: data.expiryDate?.value || "",
        expiryTime: data.expiryTime?.at || "",
        autoPublishResultsForFlexibleTiming: data.autoPublishResultsForFlexibleTiming ?? true,
        passPercentage: data.passPercentage.value,
        batches,
        subject: data.records[0]?.subject?.uuid || "",
    })
    Object.assign(form, cloneDeep(initForm))

    fetchData.batches = batches

    fetchData.isLoaded = true
}
</script>
