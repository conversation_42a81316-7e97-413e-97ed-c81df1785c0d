<template>
    <PageHeader
        :title="
            $trans(route.meta.trans, {
                attribute: $trans(route.meta.label),
            })
        "
        :navs="[
            { label: $trans('ai.ai'), path: 'AIIndex' },
            {
                label: $trans('ai.provider.providers'),
                path: 'AIProviders',
            },
        ]"
    >
        <PageHeaderAction
            name="AIProvider"
            :title="$trans('ai.provider.provider')"
            :actions="['list']"
        />
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <ShowItem
            :init-url="initUrl"
            :uuid="route.params.uuid"
            @setItem="setItem"
            @redirectTo="router.push({ name: 'AIProviders' })"
        >
            <BaseCard v-if="provider.uuid">
                <template #title>
                    {{ provider.name }}
                </template>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                    <BaseDataView :label="$trans('ai.provider.props.name')">
                        {{ provider.name }}
                    </BaseDataView>
                    <BaseDataView :label="$trans('ai.provider.props.type')">
                        <div class="flex items-center">
                            <i 
                                :class="provider.type_icon" 
                                :style="{ color: provider.type_color }"
                                class="me-2"
                            ></i>
                            {{ provider.type_name }}
                        </div>
                    </BaseDataView>
                    <BaseDataView 
                        v-if="provider.description"
                        :label="$trans('ai.provider.props.description')"
                    >
                        {{ provider.description }}
                    </BaseDataView>
                    <BaseDataView :label="$trans('ai.provider.props.api_endpoint')">
                        {{ provider.api_endpoint }}
                    </BaseDataView>
                    <BaseDataView :label="$trans('ai.provider.props.priority')">
                        {{ provider.priority }}
                    </BaseDataView>
                    <BaseDataView :label="$trans('ai.provider.props.status')">
                        <span :class="['badge', provider.is_active ? 'bg-success' : 'bg-secondary']">
                            {{ provider.is_active ? $trans('general.active') : $trans('general.inactive') }}
                        </span>
                    </BaseDataView>
                    <BaseDataView :label="$trans('general.scope')">
                        <span :class="['badge', provider.is_global ? 'bg-primary' : 'bg-info']">
                            {{ provider.is_global ? $trans('general.global') : $trans('general.team') }}
                        </span>
                    </BaseDataView>
                    <BaseDataView :label="$trans('ai.provider.props.api_key')">
                        <span :class="['badge', provider.has_api_key ? 'bg-success' : 'bg-warning']">
                            {{ provider.has_api_key ? $trans('general.configured') : $trans('general.not_configured') }}
                        </span>
                    </BaseDataView>
                    <BaseDataView 
                        v-if="provider.capabilities && provider.capabilities.length > 0"
                        :label="$trans('ai.provider.props.capabilities')"
                    >
                        <div class="d-flex flex-wrap gap-1">
                            <span 
                                v-for="capability in provider.capabilities" 
                                :key="capability"
                                class="badge bg-secondary"
                            >
                                {{ capability }}
                            </span>
                        </div>
                    </BaseDataView>
                    <BaseDataView :label="$trans('general.created_at')">
                        {{ moment(provider.created_at).format('MMMM DD, YYYY [at] HH:mm') }}
                    </BaseDataView>
                    <BaseDataView 
                        v-if="provider.updated_at"
                        :label="$trans('general.updated_at')"
                    >
                        {{ moment(provider.updated_at).format('MMMM DD, YYYY [at] HH:mm') }}
                    </BaseDataView>
                </dl>
            </BaseCard>
        </ShowItem>
    </ParentTransition>
</template>

<script>
export default {
    name: "AIProviderShow",
}
</script>

<script setup>
import { reactive, inject } from "vue"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

const $trans = inject("$trans")
const moment = inject("moment")

const initUrl = "ai/providers/"
const provider = reactive({})

const setItem = (data) => {
    Object.assign(provider, data)
}
</script>
