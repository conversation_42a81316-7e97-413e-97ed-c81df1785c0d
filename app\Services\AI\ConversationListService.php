<?php

namespace App\Services\AI;

use App\Contracts\ListGenerator;
use App\Http\Resources\AI\ConversationResource;
use App\Models\AI\Conversation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ConversationListService extends ListGenerator
{
    protected $allowedSorts = ['created_at', 'last_message_at', 'title', 'type'];
    protected $defaultSort = 'last_message_at';
    protected $defaultOrder = 'desc';

    public function getHeaders(): array
    {
        $headers = [
            [
                'key' => 'title',
                'label' => trans('ai.conversation.props.title'),
                'print_label' => 'title',
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'type',
                'label' => trans('ai.conversation.props.type'),
                'print_label' => 'type_name',
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'context',
                'label' => trans('ai.conversation.props.context'),
                'print_label' => 'context',
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'last_message_at',
                'label' => trans('ai.conversation.props.last_message'),
                'print_label' => 'latest_message.content',
                'print_sub_label' => 'latest_message.created_at.formatted',
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'created_at',
                'label' => trans('general.created_at'),
                'print_label' => 'created_at.formatted',
                'sortable' => true,
                'visibility' => true,
            ],
        ];

        if (request()->ajax()) {
            $headers[] = $this->actionHeader;
        }

        return $headers;
    }

    public function filter(Request $request): Builder
    {
        $query = Conversation::query()
            ->byTeam()
            ->with(['user', 'latestMessage'])
            ->filter([
                'App\QueryFilters\UuidMatch',
                'App\QueryFilters\LikeMatch:search,title',
                'App\QueryFilters\ExactMatch:type',
                'App\QueryFilters\ExactMatch:context',
            ]);

        // Filter by user if not admin
        if (!$request->user()->can('ai-conversation:view-all')) {
            $query->byUser();
        }

        return $query;
    }

    public function paginate(Request $request): AnonymousResourceCollection
    {
        return ConversationResource::collection($this->filter($request)
            ->orderBy($this->getSort(), $this->getOrder())
            ->paginate((int) $this->getPageLength(), ['*'], 'current_page'))
            ->additional([
                'headers' => $this->getHeaders(),
                'meta' => [
                    'allowed_sorts' => $this->allowedSorts,
                    'default_sort' => $this->defaultSort,
                    'default_order' => $this->defaultOrder,
                ],
            ]);
    }

    public function list(Request $request): AnonymousResourceCollection
    {
        return $this->paginate($request);
    }
}
