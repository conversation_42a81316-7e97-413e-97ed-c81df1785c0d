<?php

namespace App\Models\AI;

use App\Concerns\HasConfig;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Provider extends Model
{
    use HasConfig, HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $table = 'ai_providers';

    protected $casts = [
        'config' => 'array',
        'capabilities' => 'array',
        'rate_limits' => 'array',
        'meta' => 'array',
        'is_active' => 'boolean',
        'is_global' => 'boolean',
    ];

    public function getModelName(): string
    {
        return 'AI Provider';
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function conversations(): HasMany
    {
        return $this->hasMany(Conversation::class, 'provider_id');
    }

    public function usageLogs(): HasMany
    {
        return $this->hasMany(UsageLog::class, 'provider_used', 'slug');
    }

    public function scopeByTeam(Builder $query, ?int $teamId = null): Builder
    {
        $teamId = $teamId ?? auth()->user()?->current_team_id;

        // Only return tenant-specific providers (global providers come from .env)
        return $query->where('team_id', $teamId);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority', 'desc');
    }

    public function hasCapability(string $capability): bool
    {
        return in_array($capability, $this->capabilities ?? []);
    }

    public function getApiKey(): ?string
    {
        // For virtual global providers, get from config array directly
        if (is_array($this->config) && isset($this->config['api_key'])) {
            return $this->config['api_key'];
        }

        // For database providers, use the trait method
        return $this->getConfig('api_key');
    }

    public function setApiKey(string $apiKey): void
    {
        $this->setConfig(['api_key' => $apiKey], true);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('ai_provider')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
