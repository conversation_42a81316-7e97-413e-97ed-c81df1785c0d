<template>
    <div>
        <PageHeader
            :title="$trans('ai.academic.academic_chat')"
            :navs="[
                { label: $trans('ai.ai'), path: 'AIIndex' },
            ]"
        />

        <ParentTransition appear :visibility="true">
            <div class="container-xl">
                <div class="row">
                    <!-- Chat Interface -->
                    <div class="col-lg-8">
                        <BaseCard>
                            <template #header>
                                <CardHeader
                                    :title="$trans('ai.academic.academic_chat')"
                                    icon="fas fa-graduation-cap"
                                >
                                    <template #actions>
                                        <span v-if="route.query.conversationId" class="badge bg-info me-2">
                                            {{ $trans('ai.conversation.continuing_conversation') }}
                                        </span>
                                        <BaseButton
                                            @click="showHistoryModal = true"
                                            design="white"
                                            size="sm"
                                            class="me-2"
                                            v-tooltip="$trans('ai.conversation.conversations')"
                                        >
                                            <i class="fas fa-history"></i>
                                        </BaseButton>
                                        <BaseSelect
                                            v-model="selectedSubject"
                                            :options="subjectOptions"
                                            :placeholder="$trans('ai.academic.select_subject')"
                                            size="sm"
                                            class="w-auto"
                                        />
                                    </template>
                                </CardHeader>
                            </template>

                            <!-- Chat Messages Area -->
                            <div class="chat-container" ref="chatContainer">
                                <!-- Empty State -->
                                <div v-if="messages.length === 0" class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h3 class="empty-state-title">{{ $trans('ai.academic.welcome') }}</h3>
                                    <p class="empty-state-subtitle">{{ $trans('ai.academic.welcome_message') }}</p>

                                    <!-- Example Questions -->
                                    <div class="mt-4">
                                        <p class="text-muted mb-2">{{ $trans('ai.academic.example_questions') }}:</p>
                                        <div class="d-flex flex-wrap gap-2 justify-content-center">
                                            <BaseButton
                                                v-for="example in exampleQuestions"
                                                :key="example"
                                                @click="useExampleQuestion(example)"
                                                design="white"
                                                size="sm"
                                                :disabled="isLoading"
                                            >
                                                {{ example }}
                                            </BaseButton>
                                        </div>
                                    </div>
                                </div>

                                <!-- Messages -->
                                <div v-else class="messages-list">
                                    <div v-for="message in messages" :key="message.id" class="message-wrapper">
                                        <div :class="['message', message.role === 'user' ? 'message-user' : 'message-assistant']">
                                            <div class="message-avatar">
                                                <i :class="message.role === 'user' ? 'fas fa-user' : 'fas fa-graduation-cap'"></i>
                                            </div>
                                            <div class="message-content">
                                                <div class="message-bubble">
                                                    <div v-html="formatMessage(message.content)"></div>

                                                    <!-- Related Knowledge -->
                                                    <div v-if="message.relevantKnowledge && message.relevantKnowledge.length > 0" class="message-knowledge">
                                                        <TextMuted class="mb-2">{{ $trans('ai.academic.related_content') }}:</TextMuted>
                                                        <div class="d-flex flex-wrap gap-1">
                                                            <span
                                                                v-for="knowledge in message.relevantKnowledge"
                                                                :key="knowledge.title"
                                                                class="badge bg-secondary"
                                                            >
                                                                {{ knowledge.title }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="message-time">
                                                    <TextMuted>{{ message.timestamp }}</TextMuted>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Typing Indicator -->
                                    <div v-if="isLoading" class="message-wrapper">
                                        <div class="message message-assistant">
                                            <div class="message-avatar">
                                                <i class="fas fa-graduation-cap"></i>
                                            </div>
                                            <div class="message-content">
                                                <div class="message-bubble">
                                                    <div class="typing-indicator">
                                                        <span></span>
                                                        <span></span>
                                                        <span></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Input Area -->
                            <template #footer>
                                <div class="chat-input-area">
                                    <form @submit.prevent="askQuestion" class="chat-form">
                                        <div class="chat-input-container">
                                            <div class="chat-input-wrapper">
                                                <textarea
                                                    v-model="currentQuestion"
                                                    :placeholder="$trans('ai.academic.ask_question')"
                                                    :disabled="isLoading"
                                                    ref="questionInput"
                                                    class="chat-input-field"
                                                    rows="1"
                                                    @keydown.enter.exact.prevent="askQuestion"
                                                    @keydown.enter.shift.exact="addNewLine"
                                                    @input="adjustTextareaHeight"
                                                ></textarea>
                                                <BaseButton
                                                    type="submit"
                                                    design="primary"
                                                    :disabled="!currentQuestion.trim() || isLoading"
                                                    class="chat-send-btn"
                                                >
                                                    <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
                                                    <i v-else class="fas fa-paper-plane"></i>
                                                </BaseButton>
                                            </div>
                                            <div class="chat-input-hint">
                                                <TextMuted>{{ $trans('ai.academic.input_hint') }}</TextMuted>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </template>
                        </BaseCard>
                    </div>

                    <!-- Tools Sidebar -->
                    <div class="col-lg-4">
                        <div class="d-flex flex-column gap-3">
                            <!-- Academic Tools -->
                            <BaseCard>
                                <template #header>
                                    <CardHeader
                                        :title="$trans('ai.academic.tools')"
                                        icon="fas fa-tools"
                                    />
                                </template>

                                <div class="d-grid gap-2">
                                    <BaseButton
                                        v-if="perform('ai:generate-lesson-plan')"
                                        @click="showLessonPlanModal = true"
                                        design="white"
                                        class="w-100 text-start"
                                    >
                                        <i class="fas fa-chalkboard-teacher me-2"></i>
                                        {{ $trans('ai.academic.generate_lesson_plan') }}
                                    </BaseButton>

                                    <BaseButton
                                        v-if="perform('ai:generate-quiz')"
                                        @click="showQuizModal = true"
                                        design="white"
                                        class="w-100 text-start"
                                    >
                                        <i class="fas fa-clipboard-list me-2"></i>
                                        {{ $trans('ai.academic.generate_quiz') }}
                                    </BaseButton>
                                </div>
                            </BaseCard>

                            <!-- Context Settings -->
                            <BaseCard>
                                <template #header>
                                    <CardHeader
                                        :title="$trans('ai.academic.context_settings')"
                                        icon="fas fa-cog"
                                    />
                                </template>

                                <div class="d-flex flex-column gap-3">
                                    <BaseSelect
                                        v-model="selectedSubject"
                                        :label="$trans('ai.knowledge.props.subject')"
                                        :options="subjectOptions"
                                        :placeholder="$trans('general.select')"
                                    />

                                    <BaseSelect
                                        v-model="selectedLevel"
                                        :label="$trans('ai.knowledge.props.level')"
                                        :options="levelOptions"
                                        :placeholder="$trans('general.select')"
                                    />

                                    <BaseSelect
                                        v-model="selectedCategory"
                                        :label="$trans('ai.knowledge.props.category')"
                                        :options="categoryOptions"
                                        :placeholder="$trans('general.select')"
                                    />
                                </div>
                            </BaseCard>
                        </div>
                    </div>
                </div>
            </div>
        </ParentTransition>

        <!-- Lesson Plan Modal -->
        <BaseModal
            :visibility="showLessonPlanModal"
            @close="showLessonPlanModal = false"
        >
            <template #title>
                {{ $trans('ai.academic.generate_lesson_plan') }}
            </template>

            <form @submit.prevent="generateLessonPlan">
                <div class="mb-3">
                    <BaseInput
                        v-model="lessonPlan.topic"
                        :label="$trans('ai.academic.topic')"
                        type="text"
                        required
                    />
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <BaseSelect
                                v-model="lessonPlan.subject"
                                :label="$trans('ai.knowledge.props.subject')"
                                :options="subjectOptions"
                                :placeholder="$trans('general.select')"
                                required
                            />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <BaseSelect
                                v-model="lessonPlan.level"
                                :label="$trans('ai.knowledge.props.level')"
                                :options="levelOptions"
                                :placeholder="$trans('general.select')"
                                required
                            />
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <BaseInput
                        v-model="lessonPlan.duration"
                        :label="$trans('ai.academic.duration_minutes')"
                        type="number"
                        min="10"
                        max="300"
                    />
                </div>
            </form>

            <div class="mt-4 d-flex justify-content-end gap-2">
                <BaseButton
                    @click="showLessonPlanModal = false"
                    design="secondary"
                >
                    {{ $trans('general.cancel') }}
                </BaseButton>
                <BaseButton
                    @click="generateLessonPlan"
                    design="primary"
                    :disabled="isGeneratingLessonPlan"
                >
                    <i v-if="isGeneratingLessonPlan" class="fas fa-spinner fa-spin me-2"></i>
                    {{ $trans('ai.academic.generate') }}
                </BaseButton>
            </div>
        </BaseModal>

        <!-- Quiz Modal -->
        <BaseModal
            :visibility="showQuizModal"
            @close="showQuizModal = false"
        >
            <template #title>
                {{ $trans('ai.academic.generate_quiz') }}
            </template>

            <form @submit.prevent="generateQuiz">
                <div class="mb-3">
                    <BaseInput
                        v-model="quiz.topic"
                        :label="$trans('ai.academic.topic')"
                        type="text"
                        required
                    />
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <BaseSelect
                                v-model="quiz.subject"
                                :label="$trans('ai.knowledge.props.subject')"
                                :options="subjectOptions"
                                :placeholder="$trans('general.select')"
                                required
                            />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <BaseSelect
                                v-model="quiz.level"
                                :label="$trans('ai.knowledge.props.level')"
                                :options="levelOptions"
                                :placeholder="$trans('general.select')"
                                required
                            />
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <BaseInput
                        v-model="quiz.questionCount"
                        :label="$trans('ai.academic.question_count')"
                        type="number"
                        min="1"
                        max="50"
                    />
                </div>
            </form>

            <div class="mt-4 d-flex justify-content-end gap-2">
                <BaseButton
                    @click="showQuizModal = false"
                    design="secondary"
                >
                    {{ $trans('general.cancel') }}
                </BaseButton>
                <BaseButton
                    @click="generateQuiz"
                    design="primary"
                    :disabled="isGeneratingQuiz"
                >
                    <i v-if="isGeneratingQuiz" class="fas fa-spinner fa-spin me-2"></i>
                    {{ $trans('ai.academic.generate') }}
                </BaseButton>
            </div>
        </BaseModal>

        <!-- Conversation History Modal -->
        <BaseModal
            :visibility="showHistoryModal"
            @close="showHistoryModal = false"
            size="lg"
        >
            <template #title>
                {{ $trans('ai.conversation.conversations') }}
            </template>

            <div v-if="loadingHistory" class="text-center py-4">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">{{ $trans('general.loading') }}</span>
                </div>
            </div>

            <div v-else-if="conversationHistory.length === 0" class="text-center py-4">
                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                <p class="text-muted">{{ $trans('general.no_records_found') }}</p>
            </div>

            <div v-else class="list-group">
                <div
                    v-for="conversation in conversationHistory"
                    :key="conversation.uuid"
                    class="list-group-item list-group-item-action d-flex justify-content-between align-items-start"
                    @click="continueConversation(conversation)"
                    style="cursor: pointer;"
                >
                    <div class="ms-2 me-auto">
                        <div class="fw-bold">{{ conversation.title }}</div>
                        <small class="text-muted">
                            {{ conversation.latest_message ? conversation.latest_message.content.substring(0, 100) + '...' : $trans('general.no_messages') }}
                        </small>
                        <br>
                        <small class="text-muted">
                            {{ moment(conversation.updated_at).fromNow() }}
                        </small>
                    </div>
                    <span class="badge bg-primary rounded-pill">
                        {{ conversation.messages_count || 0 }}
                    </span>
                </div>
            </div>

            <div class="mt-3 d-flex justify-content-end">
                <BaseButton
                    @click="showHistoryModal = false"
                    design="secondary"
                >
                    {{ $trans('general.close') }}
                </BaseButton>
            </div>
        </BaseModal>
    </div>
</template>

<script>
export default {
    name: 'AIAcademic',
}
</script>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, inject, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useToast } from 'vue-toastification'
import { perform } from '@core/helpers/action'

const route = useRoute()
const router = useRouter()
const store = useStore()
const toast = useToast()
const $trans = inject('$trans')
const moment = inject('moment')

// Reactive data
const messages = ref([])
const currentQuestion = ref('')
const selectedSubject = ref('')
const selectedLevel = ref('sss')
const selectedCategory = ref('nigerian_cambridge')
const showLessonPlanModal = ref(false)
const showQuizModal = ref(false)
const showHistoryModal = ref(false)
const conversationHistory = ref([])
const loadingHistory = ref(false)
const currentConversationId = ref(null)

const lessonPlan = reactive({
    topic: '',
    subject: '',
    level: '',
    duration: 40,
})

const quiz = reactive({
    topic: '',
    subject: '',
    level: '',
    questionCount: 10,
})

const subjects = ref([
    { value: 'mathematics', label: 'Mathematics' },
    { value: 'english', label: 'English Language' },
    { value: 'physics', label: 'Physics' },
    { value: 'chemistry', label: 'Chemistry' },
    { value: 'biology', label: 'Biology' },
    { value: 'economics', label: 'Economics' },
    { value: 'geography', label: 'Geography' },
    { value: 'history', label: 'History' },
    { value: 'literature', label: 'Literature' },
    { value: 'government', label: 'Government' },
])

const levels = ref([
    { value: 'primary', label: 'Primary (1-6)' },
    { value: 'jss', label: 'Junior Secondary (JSS 1-3)' },
    { value: 'sss', label: 'Senior Secondary (SSS 1-3)' },
])

const categories = ref([
    { value: 'nigerian_cambridge', label: 'Nigerian Cambridge' },
    { value: 'waec', label: 'WAEC' },
    { value: 'igcse', label: 'IGCSE' },
    { value: 'a_levels', label: 'A-Levels' },
])

const exampleQuestions = ref([
    'Explain photosynthesis',
    'Solve quadratic equations',
    'Nigerian independence history',
    'Chemical bonding types',
])

// Computed properties
const isLoading = computed(() => store.getters['ai/academic/isLoading'])
const isGeneratingLessonPlan = computed(() => store.getters['ai/academic/isGeneratingLessonPlan'])
const isGeneratingQuiz = computed(() => store.getters['ai/academic/isGeneratingQuiz'])

// Options for selects
const subjectOptions = computed(() => subjects.value.map(subject => ({
    value: subject.value,
    label: subject.label
})))

const levelOptions = computed(() => levels.value.map(level => ({
    value: level.value,
    label: level.label
})))

const categoryOptions = computed(() => categories.value.map(category => ({
    value: category.value,
    label: category.label
})))

// Template refs
const chatContainer = ref(null)
const questionInput = ref(null)

// Methods
const askQuestion = async () => {
    if (!currentQuestion.value.trim()) return

    const userMessage = {
        id: Date.now(),
        role: 'user',
        content: currentQuestion.value,
        timestamp: new Date(),
    }

    messages.value.push(userMessage)
    const questionText = currentQuestion.value
    currentQuestion.value = ''

    try {
        const response = await store.dispatch('ai/academic/askQuestion', {
            question: questionText,
            subject: selectedSubject.value,
            level: selectedLevel.value,
            category: selectedCategory.value,
        })

        const assistantMessage = {
            id: Date.now() + 1,
            role: 'assistant',
            content: response.result.message.content,
            relevantKnowledge: response.result.relevantKnowledge || [],
            timestamp: new Date(),
        }

        messages.value.push(assistantMessage)

        // Store conversation ID for future reference
        if (response.result.conversationId) {
            currentConversationId.value = response.result.conversationId

            // Update URL to include conversation ID for persistence
            const newQuery = { ...route.query, conversationId: response.result.conversationId }
            router.replace({ query: newQuery })
        }

        scrollToBottom()
    } catch (error) {
        const errorMessage = {
            id: Date.now() + 1,
            role: 'assistant',
            content: 'I apologize, but I encountered an error while processing your question. Please try again.',
            timestamp: new Date(),
        }
        messages.value.push(errorMessage)
        toast.error(error.message || 'Failed to get answer')
    }
}

const generateLessonPlan = async () => {
    try {
        const response = await store.dispatch('ai/academic/generateLessonPlan', lessonPlan)

        const lessonPlanMessage = {
            id: Date.now(),
            role: 'assistant',
            content: response.result.lessonPlan.content,
            timestamp: new Date(),
        }

        messages.value.push(lessonPlanMessage)
        showLessonPlanModal.value = false
        scrollToBottom()
        // Success toast is already shown by the store action
    } catch (error) {
        toast.error(error.message || 'Failed to generate lesson plan')
    }
}

const generateQuiz = async () => {
    try {
        const response = await store.dispatch('ai/academic/generateQuiz', quiz)

        const quizMessage = {
            id: Date.now(),
            role: 'assistant',
            content: response.result.quiz.content,
            timestamp: new Date(),
        }

        messages.value.push(quizMessage)
        showQuizModal.value = false
        scrollToBottom()
        // Success toast is already shown by the store action
    } catch (error) {
        toast.error(error.message || 'Failed to generate quiz')
    }
}

const useExampleQuestion = (question) => {
    currentQuestion.value = question
    focusInput()
}

const formatMessage = (content) => {
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
}

const scrollToBottom = () => {
    nextTick(() => {
        if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight
        }
    })
}

const focusInput = () => {
    nextTick(() => {
        if (questionInput.value) {
            questionInput.value.focus()
        }
    })
}

const addNewLine = () => {
    currentQuestion.value += '\n'
    nextTick(() => {
        adjustTextareaHeight()
    })
}

const adjustTextareaHeight = () => {
    nextTick(() => {
        if (questionInput.value) {
            questionInput.value.style.height = 'auto'
            questionInput.value.style.height = Math.min(questionInput.value.scrollHeight, 120) + 'px'
        }
    })
}

const loadConversationHistory = async () => {
    const conversationId = route.query.conversationId
    if (conversationId) {
        try {
            const response = await store.dispatch('ai/conversation/fetchMessages', {
                conversationUuid: conversationId,
            })

            // Convert backend messages to frontend format
            const conversationMessages = response.data.map(msg => ({
                id: msg.uuid,
                role: msg.role,
                content: msg.content,
                timestamp: msg.createdAt.formatted,
                relevantKnowledge: msg.metadata?.relevantKnowledge || [],
            }))

            messages.value = conversationMessages
            currentConversationId.value = conversationId
            scrollToBottom()

            // Set context from conversation if available
            if (response.data.length > 0) {
                const conversation = response.data[0].conversation
                if (conversation?.contextData) {
                    selectedSubject.value = conversation.contextData.subject || ''
                    selectedLevel.value = conversation.contextData.level || ''
                    selectedCategory.value = conversation.contextData.category || 'nigerian_cambridge'
                }
            }
        } catch (error) {
            toast.error('Failed to load conversation history')
        }
    }
}

const fetchConversationHistory = async () => {
    loadingHistory.value = true
    try {
        const response = await store.dispatch('ai/conversation/list', {
            type: 'academic_chat',
            per_page: 20,
        })
        conversationHistory.value = response.data
    } catch (error) {
        toast.error('Failed to load conversation history')
    } finally {
        loadingHistory.value = false
    }
}

const continueConversation = (conversation) => {
    showHistoryModal.value = false
    router.push({
        name: 'AIAcademic',
        query: { conversationId: conversation.uuid }
    })
}

// Watch for history modal opening
watch(showHistoryModal, (newValue) => {
    if (newValue) {
        fetchConversationHistory()
    }
})

// Lifecycle
onMounted(() => {
    loadConversationHistory()
    focusInput()
})
</script>

<style scoped>
/* Chat Container */
.chat-container {
    height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--tblr-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--tblr-body-color);
}

.empty-state-subtitle {
    color: var(--tblr-text-muted);
    margin-bottom: 0;
}

/* Messages List */
.messages-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Message Wrapper */
.message-wrapper {
    display: flex;
    width: 100%;
}

.message {
    display: flex;
    max-width: 80%;
    gap: 0.75rem;
}

.message-user {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-assistant {
    margin-right: auto;
}

/* Message Avatar */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.message-user .message-avatar {
    background: var(--tblr-primary);
    color: white;
}

.message-assistant .message-avatar {
    background: var(--tblr-secondary);
    color: white;
}

/* Message Content */
.message-content {
    flex: 1;
    min-width: 0;
}

.message-bubble {
    padding: 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
    line-height: 1.5;
}

.message-user .message-bubble {
    background: var(--tblr-bg-surface);
    border: 1px solid var(--tblr-border-color);
    color: var(--tblr-body-color);
    border-bottom-right-radius: 0.25rem;
}

.message-assistant .message-bubble {
    background: var(--tblr-bg-surface);
    border: 1px solid var(--tblr-border-color);
    color: var(--tblr-body-color);
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    margin-top: 0.25rem;
    font-size: 0.75rem;
}

.message-user .message-time {
    text-align: right;
}

/* Message Knowledge */
.message-knowledge {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.message-assistant .message-knowledge {
    border-top-color: var(--tblr-border-color);
}

/* Chat Input Area */
.chat-input-area {
    padding: 1.5rem;
    border-top: 1px solid var(--tblr-border-color);
    background: var(--tblr-bg-surface);
}

.chat-form {
    margin-bottom: 0;
}

.chat-input-container {
    width: 100%;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--tblr-border-color);
    border-radius: 1rem;
    background: var(--tblr-body-bg);
    transition: border-color 0.2s ease;
}

.chat-input-wrapper:focus-within {
    border-color: var(--tblr-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--tblr-primary-rgb), 0.25);
}

.chat-input-field {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--tblr-body-color);
    min-height: 1.5rem;
    max-height: 120px;
    overflow-y: auto;
}

.chat-input-field::placeholder {
    color: var(--tblr-text-muted);
}

.chat-send-btn {
    flex-shrink: 0;
    border-radius: 0.75rem;
    padding: 0.5rem 1rem;
}

.chat-input-hint {
    margin-top: 0.5rem;
    text-align: center;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--tblr-text-muted);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .message {
        max-width: 90%;
    }

    .chat-container {
        height: 500px;
    }

    .empty-state {
        padding: 1rem;
    }

    .empty-state-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
</style>
