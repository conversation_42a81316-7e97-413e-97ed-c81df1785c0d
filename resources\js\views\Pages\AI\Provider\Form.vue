<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :init-form="initForm"
        :form="form"
        :set-form="setForm"
        redirect="AIProviders"
    >
        <div class="grid grid-cols-3 gap-6">
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="text"
                    v-model="form.name"
                    name="name"
                    :label="$trans('ai.provider.props.name')"
                    v-model:error="formErrors.name"
                    autofocus
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :options="preRequisites.providerTypes"
                    :label="$trans('ai.provider.props.type')"
                    v-model:error="formErrors.type"
                    @change="onTypeChange"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="number"
                    v-model="form.priority"
                    name="priority"
                    :label="$trans('ai.provider.props.priority')"
                    v-model:error="formErrors.priority"
                    min="0"
                    max="100"
                />
            </div>
            <div class="col-span-3">
                <BaseTextarea
                    v-model="form.description"
                    name="description"
                    :label="$trans('ai.provider.props.description')"
                    v-model:error="formErrors.description"
                    :rows="2"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="url"
                    v-model="form.api_endpoint"
                    name="api_endpoint"
                    :label="$trans('ai.provider.props.api_endpoint')"
                    v-model:error="formErrors.api_endpoint"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    type="password"
                    v-model="form.api_key"
                    name="api_key"
                    :label="$trans('ai.provider.props.api_key')"
                    v-model:error="formErrors.api_key"
                    :placeholder="route.meta.type === 'edit' ? $trans('ai.provider.leave_blank_to_keep_existing') : ''"
                />
                <div v-if="route.meta.type === 'edit'" class="text-muted text-sm mt-1">
                    {{ $trans('ai.provider.api_key_edit_help') }}
                </div>
            </div>
            <div class="col-span-3 sm:col-span-1">
                <div class="space-y-2">
                    <BaseSwitch
                        v-model="form.is_active"
                        name="is_active"
                        :label="$trans('ai.provider.props.is_active')"
                        v-model:error="formErrors.is_active"
                    />

                </div>
            </div>
            <div class="col-span-3">
                <BaseMultiCheckbox
                    :label="$trans('ai.provider.props.capabilities')"
                    :options="capabilityOptions"
                    v-model="form.capabilities"
                    v-model:error="formErrors.capabilities"
                    horizontal
                />
            </div>
            <div class="col-span-3" v-if="showRateLimits">
                <label class="form-label">{{ $trans('ai.provider.props.rate_limits') }}</label>
                <div class="grid grid-cols-3 gap-4 mt-2">
                    <BaseInput
                        type="number"
                        v-model="form.rate_limits.requests_per_minute"
                        name="rate_limits.requests_per_minute"
                        :label="$trans('ai.config.props.rate_limit_requests_per_minute')"
                        v-model:error="formErrors['rate_limits.requests_per_minute']"
                        min="1"
                    />
                    <BaseInput
                        type="number"
                        v-model="form.rate_limits.requests_per_hour"
                        name="rate_limits.requests_per_hour"
                        :label="$trans('ai.config.props.rate_limit_requests_per_hour')"
                        v-model:error="formErrors['rate_limits.requests_per_hour']"
                        min="1"
                    />
                    <BaseInput
                        type="number"
                        v-model="form.rate_limits.requests_per_day"
                        name="rate_limits.requests_per_day"
                        :label="$trans('ai.config.props.rate_limit_requests_per_day')"
                        v-model:error="formErrors['rate_limits.requests_per_day']"
                        min="1"
                    />
                </div>
            </div>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "AIProviderForm",
}
</script>

<script setup>
import { reactive, computed, inject } from "vue"
import { useRoute } from "vue-router"
import { getFormErrors, perform } from "@core/helpers/action"

const route = useRoute()

const $trans = inject("$trans")

const initForm = {
    name: "",
    type: "",
    description: "",
    api_endpoint: "",
    api_key: "",
    is_active: true,

    priority: 0,
    capabilities: [],
    rate_limits: {
        requests_per_minute: null,
        requests_per_hour: null,
        requests_per_day: null,
    },
}

const initUrl = "ai/provider/"
const formErrors = getFormErrors(initUrl)
const preRequisites = reactive({
    providerTypes: [],
    capabilities: {},
})

const form = reactive({ ...initForm })

const showRateLimits = computed(() => {
    return form.type && form.type !== ""
})

const capabilityOptions = computed(() => {
    return Object.entries(preRequisites.capabilities).map(([value, label]) => ({
        value,
        label
    }))
})

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const setForm = (data) => {
    Object.assign(form, {
        name: data.name || "",
        type: data.type || "",
        description: data.description || "",
        api_endpoint: data.api_endpoint || "",
        api_key: "", // Never pre-fill API key for security
        is_active: data.is_active ?? true,

        priority: data.priority || 0,
        capabilities: data.capabilities || [],
        rate_limits: {
            requests_per_minute: data.rate_limits?.requests_per_minute || null,
            requests_per_hour: data.rate_limits?.requests_per_hour || null,
            requests_per_day: data.rate_limits?.requests_per_day || null,
        },
    })
}

const onTypeChange = () => {
    // Auto-fill some fields based on provider type
    const typeDetails = getProviderTypeDetails(form.type)
    if (typeDetails) {
        if (!form.api_endpoint || route.meta.type === 'create') {
            form.api_endpoint = typeDetails.api_endpoint || ""
        }
        if (form.capabilities.length === 0 || route.meta.type === 'create') {
            form.capabilities = typeDetails.capabilities || []
        }
        if (form.priority === 0 || route.meta.type === 'create') {
            form.priority = typeDetails.priority || 0
        }
    }
}

const getProviderTypeDetails = (type) => {
    const details = {
        openai: {
            api_endpoint: "https://api.openai.com/v1",
            capabilities: ["text", "image", "voice"],
            priority: 2,
        },
        gemini: {
            api_endpoint: "https://generativelanguage.googleapis.com/v1beta",
            capabilities: ["text", "image", "voice"],
            priority: 3,
        },
        deepseek: {
            api_endpoint: "https://api.deepseek.com/v1",
            capabilities: ["text", "code"],
            priority: 1,
        },
        anthropic: {
            api_endpoint: "https://api.anthropic.com/v1",
            capabilities: ["text"],
            priority: 2,
        },
        xai: {
            api_endpoint: "https://api.x.ai/v1",
            capabilities: ["text"],
            priority: 1,
        },
    }
    return details[type] || null
}
</script>
