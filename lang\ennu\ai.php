<?php

return [
    'ai' => 'AI',
    'artificial_intelligence' => 'Artificial Intelligence',
    
    'provider' => [
        'provider' => 'AI Provider',
        'providers' => 'AI Providers',
        'add_provider' => 'Add Provider',
        'edit_provider' => 'Edit Provider',
        'delete_provider' => 'Delete Provider',
        'test_connection' => 'Test Connection',
        'update_api_key' => 'Update API Key',
        'api_key_updated' => 'API key updated successfully',
        'connection_successful' => 'Connection test successful',
        'connection_failed' => 'Connection test failed',
        'leave_blank_to_keep_existing' => 'Leave blank to keep existing key',
        'props' => [
            'name' => 'Name',
            'type' => 'Type',
            'description' => 'Description',
            'api_endpoint' => 'API Endpoint',
            'is_active' => 'Active',
            'is_global' => 'Global',
            'priority' => 'Priority',
            'capabilities' => 'Capabilities',
            'rate_limits' => 'Rate Limits',
            'api_key' => 'API Key',
            'status' => 'Status',
        ],
    ],
    
    'conversation' => [
        'conversation' => 'Conversation',
        'conversations' => 'Conversations',
        'new_conversation' => 'New Conversation',
        'delete_conversation' => 'Delete Conversation',
        'props' => [
            'title' => 'Title',
            'type' => 'Type',
            'context' => 'Context',
            'context_data' => 'Context Data',
            'last_message' => 'Last Message',
            'initial_message' => 'Initial Message',
        ],
        'continue_chat' => 'Continue Chat',
        'continue_academic_chat' => 'Continue Academic Chat',
        'continuing_conversation' => 'Continuing Conversation',
        'history' => 'Conversation History',
        'view_history' => 'View History',
        'rename' => 'Rename Conversation',
        'enter_new_title' => 'Enter new title',
        'rename_feature_coming_soon' => 'Rename feature coming soon...',
        'export_feature_coming_soon' => 'Export feature coming soon...',
    ],
    
    'message' => [
        'message' => 'Message',
        'messages' => 'Messages',
        'send_message' => 'Send Message',
        'sent' => 'Message sent successfully',
        'message_sent' => 'Message sent successfully',
        'props' => [
            'message' => 'Message',
            'context' => 'Context',
            'context_data' => 'Context Data',
            'attachments' => 'Attachments',
            'content' => 'Content',
            'role' => 'Role',
            'timestamp' => 'Timestamp',
        ],
    ],

    'knowledge' => [
        'knowledge' => 'Knowledge Base',
        'knowledge_base' => 'Knowledge Base',
        'add_knowledge' => 'Add Knowledge',
        'edit_knowledge' => 'Edit Knowledge',
        'delete_knowledge' => 'Delete Knowledge',
        'approve_knowledge' => 'Approve Knowledge',
        'approved' => 'Knowledge approved successfully',
        'search_completed' => 'Search completed',
        'props' => [
            'title' => 'Title',
            'description' => 'Description',
            'type' => 'Type',
            'category' => 'Category',
            'subject' => 'Subject',
            'level' => 'Level',
            'content' => 'Content',
            'tags' => 'Tags',
            'metadata' => 'Metadata',
            'is_public' => 'Public',
            'is_approved' => 'Approved',
            'status' => 'Status',
        ],
    ],
    
    'assistant' => [
        'assistant' => 'AI Assistant',
        'description' => 'Get help with general tasks and questions',
        'welcome' => 'Welcome to AI Assistant',
        'welcome_message' => 'How can I help you today? Ask me anything!',
        'type_message' => 'Type your message here...',
        'input_hint' => 'Press Enter to send, Shift+Enter for new line',
        'suggested_actions' => 'Suggested Actions',
        'command_processed' => 'Command processed successfully',
        'action_executed' => 'Action executed successfully',
        'action_failed' => 'Action execution failed',
        'quick_commands' => 'Quick Commands',
    ],
    
    'academic' => [
        'academic_chat' => 'Academic Chat',
        'description' => 'Get help with academic subjects and curriculum',
        'welcome' => 'Welcome to Academic Chat',
        'welcome_message' => 'Ask me any academic question and I\'ll help you learn!',
        'ask_question' => 'Ask your academic question...',
        'input_hint' => 'Press Enter to send, Shift+Enter for new line',
        'example_questions' => 'Example Questions',
        'related_content' => 'Related Content',
        'select_subject' => 'Select Subject',
        'tools' => 'Academic Tools',
        'context_settings' => 'Context Settings',
        'generate_lesson_plan' => 'Generate Lesson Plan',
        'generate_quiz' => 'Generate Quiz',
        'topic' => 'Topic',
        'duration_minutes' => 'Duration (Minutes)',
        'question_count' => 'Number of Questions',
        'generate' => 'Generate',
        'question_answered' => 'Question answered successfully',
        'lesson_plan_generated' => 'Lesson plan generated successfully',
        'quiz_generated' => 'Quiz generated successfully',
    ],
    
    'config' => [
        'config' => 'AI Configuration',
        'general_settings' => 'General Settings',
        'provider_settings' => 'Provider Settings',
        'rate_limiting' => 'Rate Limiting',
        'academic_settings' => 'Academic Settings',
        'assistant_settings' => 'Assistant Settings',
        'global_config_info' => 'Global Configuration Information',
        'global_config_info_text' => 'AI configuration can be set globally by the application administrator through environment variables. Global settings override the values below and are applied to all tenants.',
        'global_overrides_active' => 'Global Configuration Overrides Active:',
        'global_overrides_note' => 'These settings are controlled by the application administrator and cannot be changed here.',
        'props' => [
            'enable_ai' => 'Enable AI',
            'enable_assistant' => 'Enable Assistant',
            'enable_academic_chat' => 'Enable Academic Chat',
            'enable_voice_input' => 'Enable Voice Input',
            'enable_voice_output' => 'Enable Voice Output',
            'default_provider' => 'Default Provider',
            'enable_fallback' => 'Enable Fallback',
            'fallback_enabled' => 'Enable Fallback',
            'enable_rate_limit' => 'Enable Rate Limiting',
            'rate_limit_enabled' => 'Enable Rate Limiting',
            'rate_limit_requests_per_minute' => 'Requests per Minute',
            'rate_limit_requests_per_hour' => 'Requests per Hour',
            'rate_limit_requests_per_day' => 'Requests per Day',
            'auto_approve_knowledge' => 'Auto Approve Knowledge',
            'is_knowledge_public_by_default' => 'Knowledge Public by Default',
            'knowledge_public_by_default' => 'Knowledge Public by Default',
            'enable_conversation_saving' => 'Save Conversations',
            'save_conversations' => 'Save Conversations',
            'conversation_retention_days' => 'Conversation Retention (Days)',
            'enable_usage_analytics' => 'Enable Usage Analytics',
            'enable_cost_tracking' => 'Enable Cost Tracking',
            'cost_tracking_enabled' => 'Enable Cost Tracking',
            'max_tokens_per_request' => 'Max Tokens per Request',
            'max_conversation_length' => 'Max Conversation Length',
            'enable_content_moderation' => 'Enable Content Moderation',
            'academic_curriculum_focus' => 'Academic Curriculum Focus',
            'academic_default_level' => 'Academic Default Level',
            'assistant_context_modules' => 'Assistant Context Modules',
            'enable_global_openai' => 'Enable Global OpenAI',
            'global_openai_enabled' => 'Global OpenAI Enabled',
            'global_openai_api_key' => 'Global OpenAI API Key',
            'enable_global_gemini' => 'Enable Global Gemini',
            'global_gemini_enabled' => 'Global Gemini Enabled',
            'global_gemini_api_key' => 'Global Gemini API Key',
            'enable_global_deepseek' => 'Enable Global DeepSeek',
            'global_deepseek_enabled' => 'Global DeepSeek Enabled',
            'global_deepseek_api_key' => 'Global DeepSeek API Key',
            'enable_global_anthropic' => 'Enable Global Anthropic',
            'global_anthropic_enabled' => 'Global Anthropic Enabled',
            'global_anthropic_api_key' => 'Global Anthropic API Key',
            'enable_global_xai' => 'Enable Global xAI',
            'global_xai_enabled' => 'Global xAI Enabled',
            'global_xai_api_key' => 'Global xAI API Key',
        ],
    ],
    
    'types' => [
        'provider_types' => [
            'openai' => 'OpenAI',
            'gemini' => 'Google Gemini',
            'deepseek' => 'DeepSeek',
            'anthropic' => 'Anthropic Claude',
            'xai' => 'xAI Grok',
        ],
        'conversation_types' => [
            'assistant' => 'AI Assistant',
            'academic_chat' => 'Academic Chat',
        ],
        'knowledge_types' => [
            'curriculum' => 'Curriculum Content',
            'document' => 'Document',
            'faq' => 'FAQ',
            'lesson_plan' => 'Lesson Plan',
            'past_question' => 'Past Questions',
            'textbook' => 'Textbook',
            'syllabus' => 'Syllabus',
        ],
        'message_roles' => [
            'user' => 'User',
            'assistant' => 'Assistant',
            'system' => 'System',
        ],
    ],
    
    'categories' => [
        'nigerian_cambridge' => 'Nigerian Cambridge',
        'waec' => 'WAEC',
        'igcse' => 'IGCSE',
        'a_levels' => 'A-Levels',
        'nigerian_national' => 'Nigerian National Curriculum',
        'british_curriculum' => 'British Curriculum',
        'general' => 'General',
    ],
    
    'levels' => [
        'primary' => 'Primary (1-6)',
        'jss' => 'Junior Secondary (JSS 1-3)',
        'sss' => 'Senior Secondary (SSS 1-3)',
        'university' => 'University',
    ],
    
    'capabilities' => [
        'text' => 'Text Generation',
        'image' => 'Image Processing',
        'voice' => 'Voice Processing',
        'code' => 'Code Generation',
    ],

    'dashboard' => [
        'dashboard' => 'AI Dashboard',
        'quick_actions' => 'Quick Actions',
        'recent_activity' => 'Recent Activity',
        'statistics' => 'AI Statistics',
    ],

    'voice_input' => 'Voice Input',
];
