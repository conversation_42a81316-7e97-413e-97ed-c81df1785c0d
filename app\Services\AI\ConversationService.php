<?php

namespace App\Services\AI;

use App\Enums\AI\ConversationType;
use App\Models\AI\Conversation;
use App\Models\AI\Message;
use Illuminate\Http\Request;

class ConversationService
{
    public function preRequisite(Request $request): array
    {
        $conversationTypes = ConversationType::getOptions();
        
        return compact('conversationTypes');
    }

    public function create(Request $request): Conversation
    {
        \DB::beginTransaction();

        $conversation = Conversation::forceCreate($this->formatParams($request));

        // Create initial system message if needed
        if ($request->has('initial_message')) {
            Message::create([
                'conversation_id' => $conversation->id,
                'user_id' => null,
                'role' => 'system',
                'content' => $request->initial_message,
                'content_type' => 'text',
            ]);
        }

        \DB::commit();

        return $conversation;
    }

    public function update(Request $request, Conversation $conversation): Conversation
    {
        \DB::beginTransaction();

        $conversation->forceFill($this->formatParams($request, $conversation));
        $conversation->save();

        \DB::commit();

        return $conversation;
    }

    public function delete(Conversation $conversation): void
    {
        \DB::beginTransaction();

        // Delete all messages first
        $conversation->messages()->delete();
        
        // Delete usage logs
        $conversation->usageLogs()->delete();
        
        // Delete conversation
        $conversation->delete();

        \DB::commit();
    }

    public function findOrCreateConversation(string $type, ?string $context = null, ?array $contextData = null): Conversation
    {
        $userId = auth()->id();
        $teamId = auth()->user()?->current_team_id;

        // Check if conversation saving is enabled (check global config first)
        $saveConversations = \App\Services\AI\GlobalProviderService::getConfigValue('save_conversations', true);
        if (!$saveConversations) {
            // Create a temporary conversation that won't be persisted
            return new Conversation([
                'team_id' => $teamId,
                'user_id' => $userId,
                'type' => $type,
                'title' => $this->generateTitle($type, $context),
                'context' => $context,
                'context_data' => $contextData,
                'uuid' => \Str::uuid(),
            ]);
        }

        // Try to find existing conversation
        $conversation = Conversation::byTeam($teamId)
            ->byUser($userId)
            ->byType($type)
            ->when($context, function ($query) use ($context) {
                $query->byContext($context);
            })
            ->latest()
            ->first();

        if (!$conversation) {
            $conversation = Conversation::create([
                'team_id' => $teamId,
                'user_id' => $userId,
                'type' => $type,
                'title' => $this->generateTitle($type, $context),
                'context' => $context,
                'context_data' => $contextData,
            ]);
        }

        return $conversation;
    }

    public function sendMessage(Conversation $conversation, string $message): Message
    {
        $aiService = app(AIService::class);
        return $aiService->sendMessage($message, $conversation);
    }

    public function getMessages(Conversation $conversation, int $limit = 50, ?string $cursor = null): array
    {
        $query = $conversation->messages()
            ->orderBy('created_at', 'desc');

        if ($cursor) {
            $query->where('id', '<', $cursor);
        }

        $messages = $query->limit($limit)->get()->reverse();

        return [
            'data' => $messages,
            'next_cursor' => $messages->count() === $limit ? $messages->first()->id : null,
            'has_more' => $messages->count() === $limit,
        ];
    }

    private function formatParams(Request $request, ?Conversation $conversation = null): array
    {
        $formatted = [
            'type' => $request->type,
            'title' => $request->title,
            'context' => $request->context,
            'context_data' => $request->context_data ?? [],
        ];

        if (!$conversation) {
            $formatted['team_id'] = auth()->user()?->current_team_id;
            $formatted['user_id'] = auth()->id();
        }

        return $formatted;
    }

    private function generateTitle(string $type, ?string $context = null): string
    {
        $conversationType = ConversationType::tryFrom($type);
        $typeName = $conversationType ? $conversationType->getName() : ucfirst($type);
        
        if ($context) {
            return "{$typeName} - " . ucfirst(str_replace('_', ' ', $context));
        }
        
        return $typeName . ' - ' . now()->format('M j, Y g:i A');
    }
}
