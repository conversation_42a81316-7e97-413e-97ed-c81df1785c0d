<?php

namespace App\Services\AI;



class GlobalProviderService
{
    /**
     * Get global AI configuration from environment variables
     */
    public static function getGlobalConfig(): array
    {
        return [
            // Core AI Settings
            'ai_enabled' => env('AI_ENABLED', false),
            'default_provider' => env('AI_DEFAULT_PROVIDER', ''),
            'fallback_enabled' => env('AI_FALLBACK_ENABLED', true),

            // Rate Limiting
            'rate_limit_enabled' => env('AI_RATE_LIMIT_ENABLED', false),
            'rate_limit_requests_per_minute' => env('AI_RATE_LIMIT_REQUESTS_PER_MINUTE', 60),
            'rate_limit_requests_per_hour' => env('AI_RATE_LIMIT_REQUESTS_PER_HOUR', 1000),
            'rate_limit_requests_per_day' => env('AI_RATE_LIMIT_REQUESTS_PER_DAY', 10000),

            // Academic Settings
            'academic_curriculum_focus' => env('AI_ACADEMIC_CURRICULUM_FOCUS', 'nigerian_cambridge'),
            'academic_default_level' => env('AI_ACADEMIC_DEFAULT_LEVEL', 'sss'),

            // Data Settings
            'save_conversations' => env('AI_SAVE_CONVERSATIONS', true),
            'conversation_retention_days' => env('AI_CONVERSATION_RETENTION_DAYS', 90),
            'enable_usage_analytics' => env('AI_ENABLE_USAGE_ANALYTICS', true),
            'cost_tracking_enabled' => env('AI_COST_TRACKING_ENABLED', true),

            // Feature Toggles
            'enable_assistant' => env('AI_ENABLE_ASSISTANT', true),
            'enable_academic_chat' => env('AI_ENABLE_ACADEMIC_CHAT', true),
            'enable_voice_input' => env('AI_ENABLE_VOICE_INPUT', false),
            'enable_voice_output' => env('AI_ENABLE_VOICE_OUTPUT', false),
            'enable_content_moderation' => env('AI_ENABLE_CONTENT_MODERATION', false),

            // Token and Request Limits
            'max_tokens_per_request' => env('AI_MAX_TOKENS_PER_REQUEST', 4000),
            'max_conversation_length' => env('AI_MAX_CONVERSATION_LENGTH', 50),
        ];
    }

    /**
     * Check if AI is globally enabled
     */
    public static function isGloballyEnabled(): bool
    {
        return env('AI_ENABLED', false);
    }

    /**
     * Get global configuration value with fallback to database config
     */
    public static function getConfigValue(string $key, $default = null)
    {
        $globalConfig = static::getGlobalConfig();

        // If global config exists, use it
        if (array_key_exists($key, $globalConfig) && $globalConfig[$key] !== null) {
            return $globalConfig[$key];
        }

        // Fallback to database config
        return config("config.ai.{$key}", $default);
    }

    /**
     * Get available global providers from environment variables (no database)
     */
    public static function getAvailableGlobalProviders(): array
    {
        $providers = [];

        $providerConfigs = [
            'openai' => [
                'enabled' => env('AI_GLOBAL_OPENAI_ENABLED', false),
                'api_key' => env('AI_GLOBAL_OPENAI_API_KEY'),
                'name' => 'Global OpenAI',
                'description' => 'Global OpenAI provider configured by application administrator',
            ],
            'gemini' => [
                'enabled' => env('AI_GLOBAL_GEMINI_ENABLED', false),
                'api_key' => env('AI_GLOBAL_GEMINI_API_KEY'),
                'name' => 'Global Gemini',
                'description' => 'Global Google Gemini provider configured by application administrator',
            ],
            'deepseek' => [
                'enabled' => env('AI_GLOBAL_DEEPSEEK_ENABLED', false),
                'api_key' => env('AI_GLOBAL_DEEPSEEK_API_KEY'),
                'name' => 'Global DeepSeek',
                'description' => 'Global DeepSeek provider configured by application administrator',
            ],
            'anthropic' => [
                'enabled' => env('AI_GLOBAL_ANTHROPIC_ENABLED', false),
                'api_key' => env('AI_GLOBAL_ANTHROPIC_API_KEY'),
                'name' => 'Global Anthropic',
                'description' => 'Global Anthropic Claude provider configured by application administrator',
            ],
            'xai' => [
                'enabled' => env('AI_GLOBAL_XAI_ENABLED', false),
                'api_key' => env('AI_GLOBAL_XAI_API_KEY'),
                'name' => 'Global xAI',
                'description' => 'Global xAI Grok provider configured by application administrator',
            ],
        ];

        foreach ($providerConfigs as $type => $config) {
            if ($config['enabled'] && !empty($config['api_key'])) {
                $providerType = \App\Enums\AI\ProviderType::tryFrom($type);
                if ($providerType) {
                    $providers[] = [
                        'type' => $type,
                        'name' => $config['name'],
                        'description' => $config['description'],
                        'api_key' => $config['api_key'],
                        'api_endpoint' => $providerType->getApiEndpoint(),
                        'priority' => $providerType->getPriority(),
                        'capabilities' => $providerType->getCapabilities(),
                        'models' => $providerType->getModels(),
                        'is_global' => true,
                        'is_active' => true,
                    ];
                }
            }
        }

        // Sort by priority (highest first)
        usort($providers, function($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });

        return $providers;
    }

    /**
     * Get a specific global provider by type from environment
     */
    public static function getGlobalProvider(string $type): ?array
    {
        $providers = static::getAvailableGlobalProviders();

        foreach ($providers as $provider) {
            if ($provider['type'] === $type) {
                return $provider;
            }
        }

        return null;
    }

    /**
     * Check if any global providers are configured in environment
     */
    public static function hasGlobalProviders(): bool
    {
        return !empty(static::getAvailableGlobalProviders());
    }

    /**
     * Get the highest priority global provider
     */
    public static function getDefaultGlobalProvider(): ?array
    {
        $providers = static::getAvailableGlobalProviders();
        return $providers[0] ?? null;
    }
}
