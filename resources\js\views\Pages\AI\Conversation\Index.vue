<template>
    <ListItem
        :init-url="initUrl"
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        @setItems="setItems"
    >
        <template #header>
            <PageHeader
                :title="$trans('ai.conversation.conversations')"
                :navs="[
                    { label: $trans('ai.ai'), path: 'AIIndex' },
                ]"
            >
                <PageHeaderAction
                    url="ai/conversations/"
                    name="AIConversation"
                    :title="$trans('ai.conversation.conversations')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                    @toggleFilter="showFilter = !showFilter"
                />
            </PageHeader>
        </template>

        <template #filter>
            <ParentTransition appear :visibility="showFilter">
                <Filter
                    :preRequisites="preRequisites"
                    @refresh="emitter.emit('listItems')"
                    @hide="showFilter = false"
                />
            </ParentTransition>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="conversations.headers"
                :meta="conversations.meta"
                module="ai.conversation"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="conversation in conversations.data"
                    :key="conversation.uuid"
                    @double-click="
                        router.push({
                            name: 'AIConversationShow',
                            params: { uuid: conversation.uuid },
                        })
                    "
                >
                    <DataCell name="title" clickable>
                        <div class="flex items-center">
                            <div class="me-3">
                                <i
                                    :class="conversation.typeIcon"
                                    :style="{ color: conversation.typeColor }"
                                ></i>
                            </div>
                            <div>
                                <div class="font-medium">{{ conversation.title }}</div>
                                <div class="text-muted small">{{ conversation.typeName }}</div>
                            </div>
                        </div>
                    </DataCell>
                    <DataCell name="type">
                        <div class="flex items-center">
                            <div class="me-2">
                                <i
                                    :class="conversation.typeIcon"
                                    :style="{ color: conversation.typeColor }"
                                ></i>
                            </div>
                            <div>{{ conversation.typeName }}</div>
                        </div>
                    </DataCell>
                    <DataCell name="context">
                        <span v-if="conversation.context" class="badge bg-secondary">
                            {{ conversation.context }}
                        </span>
                        <span v-else class="text-muted">-</span>
                    </DataCell>
                    <DataCell name="last_message_at">
                        <div v-if="conversation.latestMessage">
                            <div class="text-truncate" style="max-width: 200px;">
                                {{ conversation.latestMessage.content }}
                            </div>
                            <small class="text-muted">
                                {{ $cal.toUserTimezone(conversation.latestMessage.createdAt.value, 'datetime') }}
                            </small>
                        </div>
                        <span v-else class="text-muted">{{ $trans('general.no_messages') }}</span>
                    </DataCell>
                    <DataCell name="created_at">
                        {{ $cal.toUserTimezone(conversation.createdAt.value, 'date') }}
                    </DataCell>
                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                v-if="perform('ai-conversation:read')"
                                icon="fas fa-arrow-circle-right"
                                @click="
                                    router.push({
                                        name: 'AIConversationShow',
                                        params: { uuid: conversation.uuid },
                                    })
                                "
                                >{{ $trans('general.show') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="conversation.type === 'assistant' && perform('ai:use-assistant')"
                                icon="fas fa-comments"
                                @click="continueChat(conversation, 'assistant')"
                                >{{ $trans('ai.conversation.continue_chat') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="conversation.type === 'academic_chat' && perform('ai:use-academic-chat')"
                                icon="fas fa-graduation-cap"
                                @click="continueChat(conversation, 'academic')"
                                >{{ $trans('ai.conversation.continue_academic_chat') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-conversation:edit')"
                                icon="fas fa-edit"
                                @click="renameConversation(conversation)"
                                >{{ $trans('ai.conversation.rename') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-conversation:export')"
                                icon="fas fa-download"
                                @click="exportConversation(conversation)"
                                >{{ $trans('general.export') }}</FloatingMenuItem
                            >
                            <FloatingMenuItem
                                v-if="perform('ai-conversation:delete')"
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: conversation.uuid,
                                    })
                                "
                                >{{ $trans('general.delete') }}</FloatingMenuItem
                            >
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "AIConversationIndex",
}
</script>

<script setup>
import { reactive, ref, inject } from "vue"
import { useRouter } from "vue-router"
import { perform } from '@core/helpers/action'
import Filter from './Filter.vue'

const router = useRouter()
const $trans = inject('$trans')
const emitter = inject('emitter')
const $cal = inject('$cal')

const initUrl = "ai/conversation/"
const showFilter = ref(false)

const preRequisites = reactive({
    conversationTypes: [],
})

const conversations = reactive({})

let userActions = ["filter"]

let dropdownActions = []
if (perform("ai-conversation:export")) {
    dropdownActions = ["print", "pdf", "excel"]
}

const setPreRequisites = (data) => {
    Object.assign(preRequisites, data)
}

const setItems = (data) => {
    Object.assign(conversations, data)
}

const continueChat = (conversation, type) => {
    if (type === 'assistant') {
        router.push({
            name: 'AIAssistant',
            query: {
                conversationId: conversation.uuid,
                context: conversation.context || 'general',
                // Properly serialize contextData as JSON string for URL
                contextData: conversation.contextData ? JSON.stringify(conversation.contextData) : '{}'
            }
        })
    } else if (type === 'academic') {
        router.push({
            name: 'AIAcademic',
            query: {
                conversationId: conversation.uuid,
                context: conversation.context || 'academic',
                // Properly serialize contextData as JSON string for URL
                contextData: conversation.contextData ? JSON.stringify(conversation.contextData) : '{}'
            }
        })
    }
}

const renameConversation = async (conversation) => {
    const newTitle = prompt($trans('ai.conversation.enter_new_title'), conversation.title)
    if (newTitle && newTitle.trim() && newTitle !== conversation.title) {
        try {
            // This would need to be implemented in the store
            // await store.dispatch('ai/conversation/update', {
            //     uuid: conversation.uuid,
            //     title: newTitle.trim()
            // })
            // For now, just show a message
            alert($trans('ai.conversation.rename_feature_coming_soon'))
        } catch (error) {
            alert($trans('general.error_occurred'))
        }
    }
}

const exportConversation = async (conversation) => {
    try {
        // This would need to be implemented in the store
        // const response = await store.dispatch('ai/conversation/export', {
        //     uuid: conversation.uuid,
        //     format: 'pdf' // or 'json', 'txt'
        // })
        // For now, just show a message
        alert($trans('ai.conversation.export_feature_coming_soon'))
    } catch (error) {
        alert($trans('general.error_occurred'))
    }
}


</script>
