<?php

namespace App\Actions\Config\Module;

class StoreAiConfig
{
    public static function handle(): array
    {
        $input = request()->validate([
            'enable_ai' => 'boolean',
            'enable_assistant' => 'boolean',
            'enable_academic_chat' => 'boolean',
            'enable_voice_input' => 'boolean',
            'enable_voice_output' => 'boolean',
            'default_provider' => 'nullable|string|max:50',
            'enable_fallback' => 'boolean',
            'enable_rate_limit' => 'boolean',
            'rate_limit_requests_per_minute' => 'nullable|integer|min:1|max:1000',
            'rate_limit_requests_per_hour' => 'nullable|integer|min:1|max:10000',
            'rate_limit_requests_per_day' => 'nullable|integer|min:1|max:100000',
            'auto_approve_knowledge' => 'boolean',
            'is_knowledge_public_by_default' => 'boolean',
            'enable_conversation_saving' => 'boolean',
            'conversation_retention_days' => 'nullable|integer|min:1|max:365',
            'enable_usage_analytics' => 'boolean',
            'enable_cost_tracking' => 'boolean',
            'max_tokens_per_request' => 'nullable|integer|min:100|max:10000',
            'max_conversation_length' => 'nullable|integer|min:10|max:100',
            'enable_content_moderation' => 'boolean',
            'academic_curriculum_focus' => 'nullable|string|max:100',
            'academic_default_level' => 'nullable|string|max:50',
            'assistant_context_modules' => 'nullable|array',
            'assistant_context_modules.*' => 'string|max:50',


        ], [], [
            'enable_ai' => __('ai.config.props.enable_ai'),
            'enable_assistant' => __('ai.config.props.enable_assistant'),
            'enable_academic_chat' => __('ai.config.props.enable_academic_chat'),
            'enable_voice_input' => __('ai.config.props.enable_voice_input'),
            'enable_voice_output' => __('ai.config.props.enable_voice_output'),
            'default_provider' => __('ai.config.props.default_provider'),
            'enable_fallback' => __('ai.config.props.enable_fallback'),
            'enable_rate_limit' => __('ai.config.props.enable_rate_limit'),
            'rate_limit_requests_per_minute' => __('ai.config.props.rate_limit_requests_per_minute'),
            'rate_limit_requests_per_hour' => __('ai.config.props.rate_limit_requests_per_hour'),
            'rate_limit_requests_per_day' => __('ai.config.props.rate_limit_requests_per_day'),
            'auto_approve_knowledge' => __('ai.config.props.auto_approve_knowledge'),
            'is_knowledge_public_by_default' => __('ai.config.props.is_knowledge_public_by_default'),
            'enable_conversation_saving' => __('ai.config.props.enable_conversation_saving'),
            'conversation_retention_days' => __('ai.config.props.conversation_retention_days'),
            'enable_usage_analytics' => __('ai.config.props.enable_usage_analytics'),
            'enable_cost_tracking' => __('ai.config.props.enable_cost_tracking'),
            'max_tokens_per_request' => __('ai.config.props.max_tokens_per_request'),
            'max_conversation_length' => __('ai.config.props.max_conversation_length'),
            'enable_content_moderation' => __('ai.config.props.enable_content_moderation'),
            'academic_curriculum_focus' => __('ai.config.props.academic_curriculum_focus'),
            'academic_default_level' => __('ai.config.props.academic_default_level'),
            'assistant_context_modules' => __('ai.config.props.assistant_context_modules'),
            'enable_global_openai' => __('ai.config.props.enable_global_openai'),

        ]);

        // Set defaults
        if (!isset($input['enable_ai'])) {
            $input['enable_ai'] = false;
        }

        if (!isset($input['enable_assistant'])) {
            $input['enable_assistant'] = true;
        }

        if (!isset($input['enable_academic_chat'])) {
            $input['enable_academic_chat'] = true;
        }

        if (!isset($input['fallback_enabled'])) {
            $input['fallback_enabled'] = true;
        }

        if (!isset($input['save_conversations'])) {
            $input['save_conversations'] = true;
        }

        if (!isset($input['enable_conversation_saving'])) {
            $input['enable_conversation_saving'] = true;
        }

        if (!isset($input['enable_usage_analytics'])) {
            $input['enable_usage_analytics'] = true;
        }

        if (!isset($input['academic_curriculum_focus'])) {
            $input['academic_curriculum_focus'] = 'nigerian_cambridge';
        }

        if (!isset($input['academic_default_level'])) {
            $input['academic_default_level'] = 'sss';
        }

        if (!isset($input['conversation_retention_days'])) {
            $input['conversation_retention_days'] = 90;
        }

        if (!isset($input['max_tokens_per_request'])) {
            $input['max_tokens_per_request'] = 4000;
        }

        if (!isset($input['max_conversation_length'])) {
            $input['max_conversation_length'] = 50;
        }

        return $input;
    }
}
