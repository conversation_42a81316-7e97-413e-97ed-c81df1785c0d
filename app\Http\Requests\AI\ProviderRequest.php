<?php

namespace App\Http\Requests\AI;

use App\Enums\AI\ProviderType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ProviderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required|string|max:100',
            'type' => ['required', new Enum(ProviderType::class)],
            'description' => 'nullable|string|max:1000',
            'api_endpoint' => 'nullable|url|max:255',
            'is_active' => 'boolean',

            'priority' => 'integer|min:0|max:100',
            'capabilities' => 'array',
            'capabilities.*' => 'string|in:text,image,voice,code',
            'rate_limits' => 'array',
            'rate_limits.requests_per_minute' => 'nullable|integer|min:1|max:10000',
            'rate_limits.requests_per_hour' => 'nullable|integer|min:1|max:100000',
            'rate_limits.requests_per_day' => 'nullable|integer|min:1|max:1000000',
            'api_key' => 'nullable|string|max:500',
        ];

        // Make name unique for the team
        if ($this->isMethod('POST')) {
            $rules['name'] .= '|unique:ai_providers,name,NULL,id,team_id,' . (auth()->user()?->current_team_id ?? 'NULL');
        } else {
            $provider = $this->route('provider');
            $rules['name'] .= '|unique:ai_providers,name,' . $provider->id . ',id,team_id,' . (auth()->user()?->current_team_id ?? 'NULL');
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => trans('ai.provider.props.name'),
            'type' => trans('ai.provider.props.type'),
            'description' => trans('ai.provider.props.description'),
            'api_endpoint' => trans('ai.provider.props.api_endpoint'),
            'is_active' => trans('ai.provider.props.is_active'),

            'priority' => trans('ai.provider.props.priority'),
            'capabilities' => trans('ai.provider.props.capabilities'),
            'rate_limits' => trans('ai.provider.props.rate_limits'),
            'api_key' => trans('ai.provider.props.api_key'),
        ];
    }
}
