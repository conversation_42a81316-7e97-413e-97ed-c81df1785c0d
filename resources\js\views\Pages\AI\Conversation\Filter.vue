<template>
    <FilterForm
        :init-form="initForm"
        :form="form"
        :multiple="[]"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-3 gap-6">
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    v-model="form.search"
                    name="search"
                    :label="$trans('general.search')"
                    :placeholder="$trans('ai.conversation.search_placeholder')"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('ai.conversation.props.type')"
                    :options="props.preRequisites.conversationTypes || []"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.context"
                    name="context"
                    :label="$trans('ai.conversation.props.context')"
                    :options="contextOptions"
                />
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mt-4">
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.status"
                    name="status"
                    :label="$trans('general.status')"
                    :options="statusOptions"
                />
            </div>
            <div class="col-span-3 sm:col-span-1" v-if="perform('ai-conversation:read-all')">
                <BaseSelect
                    v-model="form.user_uuid"
                    name="user_uuid"
                    :label="$trans('general.created_by')"
                    :options="userOptions"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.sort"
                    name="sort"
                    :label="$trans('general.sort_by')"
                    :options="sortOptions"
                />
            </div>
        </div>
        
        <div class="grid grid-cols-3 gap-6 mt-4">
            <div class="col-span-3 sm:col-span-1">
                <DatePicker
                    v-model="form.start_date"
                    name="start_date"
                    :label="$trans('general.start_date')"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <DatePicker
                    v-model="form.end_date"
                    name="end_date"
                    :label="$trans('general.end_date')"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseInput
                    v-model="form.message_count_min"
                    name="message_count_min"
                    type="number"
                    :label="$trans('ai.conversation.min_messages')"
                    min="0"
                />
            </div>
        </div>
    </FilterForm>
</template>

<script setup>
import { reactive, computed, inject } from "vue"
import { perform } from '@core/helpers/action'

const $trans = inject('$trans')

const emit = defineEmits(["hide", "refresh"])

const props = defineProps({
    preRequisites: {
        type: Object,
        default() {
            return {}
        },
    },
})

const initForm = {
    search: "",
    type: "",
    context: "",
    status: "",
    user_uuid: "",
    start_date: "",
    end_date: "",
    message_count_min: "",
    sort: "latest",
}

const form = reactive({ ...initForm })

const contextOptions = computed(() => ({
    "": $trans('general.all'),
    "general": $trans('general.general'),
    "student": $trans('student.student'),
    "academic": $trans('academic.academic'),
    "administrative": $trans('general.administrative'),
}))

const statusOptions = computed(() => ({
    "": $trans('general.all'),
    "active": $trans('general.active'),
    "archived": $trans('general.archived'),
    "completed": $trans('general.completed'),
}))

const userOptions = computed(() => ({
    "": $trans('general.all'),
    // This would be populated from API or props
}))

const sortOptions = computed(() => ({
    "latest": $trans('general.latest'),
    "oldest": $trans('general.oldest'),
    "most_messages": $trans('ai.conversation.most_messages'),
    "least_messages": $trans('ai.conversation.least_messages'),
    "last_activity": $trans('ai.conversation.last_activity'),
}))
</script>
