# See http://help.github.com/ignore-files/ for more about ignoring files.
docker-compose.override.yml
# compiled output
/tmp
/datadir
/out-tsc
/server/build
/src/*.js
.env
.DS_Store
*js.map
# dependencies
/node_modules
/server/node_modules
# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
# misc
newrelic_agent.log
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
# System Files
.DS_Store
Thumbs.db
#env
.env
.test.env
/dist
.nyc_output
.yarn
.prettierignore
/node_modules
!/public/build
!/public/site/build
!/public/hot
!/public/storage
!/public/sitemap.xml
/app/Custom/
/storage/*.key
!/resources/
!/resources/*
/resources/views/custom-print/
!/vendor/
/vendor/*
!/vendor/mint/
*.lock
.env
.env.backup
.phpunit.result.cache
ray.php
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode
/storage/framework/cache/*
/storage/framework/views/*
/storage/framework/sessions/*
/storage/framework/testing/*
/storage/logs/*
/storage/logs/