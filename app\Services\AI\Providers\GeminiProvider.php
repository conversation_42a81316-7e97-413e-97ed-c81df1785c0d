<?php

namespace App\Services\AI\Providers;

use App\Models\AI\Provider;

class GeminiProvider extends BaseProvider
{
    public function sendMessage(string $message, array $context, Provider $provider): array
    {
        $apiKey = $provider->getApiKey();
        if (!$apiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        
        $model = $provider->getConfig('default_model', 'gemini-1.5-flash-001');
        $endpoint = $provider->getConfig('api_endpoint', 'https://generativelanguage.googleapis.com/v1beta');
        
        $url = "{$endpoint}/models/{$model}:generateContent?key={$apiKey}";
        
        $messages = $this->formatMessages($context);
        $lastMessage = end($messages);


        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $lastMessage['content']]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $provider->getConfig('temperature', 0.7),
                'maxOutputTokens' => $provider->getConfig('max_tokens', 4000),
                'topP' => 0.8,
                'topK' => 10
            ]
        ];


        $response = $this->makeRequest($url, $data, [
            'Content-Type' => 'application/json',
        ]);

        $content = $this->extractContent($response);
        $tokens = $this->calculateTokens($message . $content);
        $cost = $this->calculateCost($tokens, $model);
        
        return [
            'content' => $content,
            'model' => $model,
            'tokens' => $tokens,
            'cost' => $cost,
            'raw_response' => $response,
        ];
    }

    public function testConnection(Provider $provider): array
    {
        try {
            $apiKey = $provider->getApiKey();
            if (!$apiKey) {
                throw new \Exception('API key not configured');
            }

            $endpoint = $provider->getConfig('api_endpoint', 'https://generativelanguage.googleapis.com/v1beta');
            $url = "{$endpoint}/models?key={$apiKey}";

            $response = $this->makeRequest($url, [], [
                'Content-Type' => 'application/json',
            ]);

            return [
                'success' => true,
                'models' => $response['models'] ?? [],
                'message' => 'Connection successful',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    protected function extractContent(array $response): string
    {
        $candidates = $response['candidates'] ?? [];
        if (empty($candidates)) {
            throw new \Exception('No response candidates from Gemini');
        }

        $content = $candidates[0]['content']['parts'][0]['text'] ?? '';
        if (empty($content)) {
            throw new \Exception('Empty response from Gemini');
        }

        return $content;
    }

    protected function calculateCost(int $tokens, string $model): float
    {
        // Gemini pricing (approximate)
        $rates = [
            'gemini-1.5-flash-001' => 0.00025, // $0.00025 per 1K tokens
            'gemini-pro-vision' => 0.00025,
        ];

        $rate = $rates[$model] ?? 0.00025;
        return ($tokens / 1000) * $rate;
    }
}
