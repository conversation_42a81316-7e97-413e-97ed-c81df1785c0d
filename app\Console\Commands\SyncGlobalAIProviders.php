<?php

namespace App\Console\Commands;

use App\Services\AI\GlobalProviderService;
use Illuminate\Console\Command;

class SyncGlobalAIProviders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:check-global-providers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check global AI providers configured in environment variables';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking global AI providers from environment variables...');

        try {
            $globalProviders = GlobalProviderService::getAvailableGlobalProviders();

            if (empty($globalProviders)) {
                $this->warn('No global providers configured in environment variables.');
                $this->line('');
                $this->line('To configure global providers, add these to your .env file:');
                $this->line('AI_GLOBAL_OPENAI_ENABLED=true');
                $this->line('AI_GLOBAL_OPENAI_API_KEY=sk-your-openai-key');
                $this->line('AI_GLOBAL_GEMINI_ENABLED=true');
                $this->line('AI_GLOBAL_GEMINI_API_KEY=your-gemini-key');
                $this->line('AI_GLOBAL_DEEPSEEK_ENABLED=true');
                $this->line('AI_GLOBAL_DEEPSEEK_API_KEY=your-deepseek-key');
                $this->line('AI_GLOBAL_ANTHROPIC_ENABLED=true');
                $this->line('AI_GLOBAL_ANTHROPIC_API_KEY=your-anthropic-key');
                $this->line('AI_GLOBAL_XAI_ENABLED=true');
                $this->line('AI_GLOBAL_XAI_API_KEY=your-xai-key');
            } else {
                $this->info('Found ' . count($globalProviders) . ' global AI providers configured:');

                foreach ($globalProviders as $provider) {
                    $this->line("  - {$provider['name']} ({$provider['type']}) - Priority: {$provider['priority']}");
                }
            }

            $this->info('Global AI providers check completed successfully.');

        } catch (\Exception $e) {
            $this->error('Failed to check global AI providers: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
