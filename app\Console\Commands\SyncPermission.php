<?php

namespace App\Console\Commands;

use App\Actions\Saas\SwitchDatabase;
use App\Concerns\GetTenantsForCommand;
use Illuminate\Console\Command;

class SyncPermission extends Command
{
    use GetTenantsForCommand;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:permission {tenant?* : ID of Tenants} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync permissions';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $force = $this->option('force');

        if (\App::environment('production') && ! $force) {
            $this->error('Could not sync in production mode');
            exit;
        }

        $tenantIds = $this->argument('tenant');

        $tenants = $this->getTenants($tenantIds);

        $bar = $this->output->createProgressBar(count($tenants));
        $bar->start();

        activity()->disableLogging();

        foreach ($tenants as $tenant) {
            $this->newLine();
            $this->info('Sync Permission tenant: '.$tenant->id);

            (new SwitchDatabase)->execute($tenant->database);

            \Artisan::call('cache:clear');

            \Artisan::call('db:seed', ['--class' => 'PermissionSeeder', '--force' => $force ? true : false]);
            \Artisan::call('db:seed', ['--class' => 'AssignPermissionSeeder', '--force' => $force ? true : false]);

            $this->info('Sync Permission complete tenant: '.$tenant->id);

            $bar->advance();
            $this->newLine();

            (new SwitchDatabase)->execute();
        }

        $bar->finish();

        activity()->enableLogging();

        $this->info('Permissions synced.');

        return 0;
    }
}
