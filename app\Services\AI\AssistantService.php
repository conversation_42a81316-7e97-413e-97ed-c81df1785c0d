<?php

namespace App\Services\AI;

use App\Models\AI\Conversation;
use App\Models\AI\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AssistantService
{
    private ConversationService $conversationService;
    private AIService $aiService;

    public function __construct(ConversationService $conversationService, AIService $aiService)
    {
        $this->conversationService = $conversationService;
        $this->aiService = $aiService;
    }

    public function processCommand(Request $request): array
    {
        $message = $request->message;
        $context = $request->context ?? 'general';
        $contextData = $request->context_data ?? [];

        // Find or create assistant conversation
        $conversation = $this->conversationService->findOrCreateConversation(
            'assistant',
            $context,
            $contextData
        );

        // First, check for application-specific actions
        $applicationActions = $this->parseApplicationActions($message);

        if (!empty($applicationActions)) {
            // Handle application-specific request directly
            $primaryAction = $applicationActions[0];
            $actionResult = $this->executeSpecificAction($primaryAction['type'], $primaryAction['parameters']);

            // Create a user message
            $userMessage = \App\Models\AI\Message::create([
                'conversation_id' => $conversation->id,
                'user_id' => auth()->id(),
                'role' => 'user',
                'content' => $message,
                'content_type' => 'text',
            ]);

            // Create assistant response with guidance
            $assistantResponse = \App\Models\AI\Message::create([
                'conversation_id' => $conversation->id,
                'user_id' => null,
                'role' => 'assistant',
                'content' => $actionResult['message'] ?? 'I can help you with that task.',
                'content_type' => 'text',
                'metadata' => [
                    'action_result' => $actionResult,
                    'actions' => $applicationActions,
                ],
            ]);

            return [
                'conversation_id' => $conversation->uuid,
                'message' => $assistantResponse,
                'actions' => $applicationActions,
                'context' => $context,
                'action_result' => $actionResult,
            ];
        }

        // If no application-specific action found, use external AI
        $enhancedMessage = $this->enhanceMessageWithContext($message, $context, $contextData);
        $response = $this->aiService->sendMessage($enhancedMessage, $conversation);
        $actions = $this->parseActionsFromResponse($response->content);

        return [
            'conversation_id' => $conversation->uuid,
            'message' => $response,
            'actions' => $actions,
            'context' => $context,
        ];
    }

    public function executeAction(Request $request): array
    {
        $action = $request->action;
        $parameters = $request->parameters ?? [];
        $conversationId = $request->conversation_id;

        try {
            // Find conversation
            $conversation = Conversation::where('uuid', $conversationId)->firstOrFail();

            // Execute the action based on type
            $result = $this->executeSpecificAction($action, $parameters);

            // Create a system message with the result
            Message::create([
                'conversation_id' => $conversation->id,
                'user_id' => null,
                'role' => 'system',
                'content' => "Action executed: {$action}. Result: " . json_encode($result),
                'content_type' => 'text',
                'metadata' => [
                    'action' => $action,
                    'parameters' => $parameters,
                    'result' => $result,
                ],
            ]);

            return [
                'success' => true,
                'action' => $action,
                'result' => $result,
                'message' => 'Action executed successfully',
            ];

        } catch (\Exception $e) {
            Log::error('Assistant action execution failed', [
                'action' => $action,
                'parameters' => $parameters,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'action' => $action,
                'error' => $e->getMessage(),
                'message' => 'Failed to execute action',
            ];
        }
    }

    private function enhanceMessageWithContext(string $message, string $context, array $contextData): string
    {
        $user = auth()->user();
        $team = $user->currentTeam ?? $user->teams?->first();

        $enhancement = "\n\nSCHOOL MANAGEMENT SYSTEM CONTEXT:";
        $enhancement .= "\nCurrent Context: {$context}";

        if (!empty($contextData)) {
            $enhancement .= "\nContext Data: " . json_encode($contextData);
        }

        // Add user information
        $enhancement .= "\nUser: " . $user->name . " (" . $user->email . ")";
        $enhancement .= "\nUser Roles: " . $user->roles->pluck('name')->implode(', ');

        // Add school/team information
        if ($team) {
            $enhancement .= "\nSchool/Institution: " . $team->name;
        }

        // Add current academic period information
        try {
            $currentPeriod = \App\Models\Academic\Period::query()
                ->byTeam()
                ->where('is_default', true)
                ->first();

            if ($currentPeriod) {
                $enhancement .= "\nCurrent Academic Period: " . $currentPeriod->name;
                $enhancement .= "\nPeriod Start: " . $currentPeriod->start_date->formatted;
                $enhancement .= "\nPeriod End: " . $currentPeriod->end_date->formatted;
            }
        } catch (\Exception $e) {
            // Ignore if period model doesn't exist or has issues
        }

        // Add today's date for attendance and other date-sensitive operations
        $enhancement .= "\nToday's Date: " . now()->format('Y-m-d (l, M d, Y)');

        // Add available modules/permissions
        $permissions = $user->getAllPermissions()->pluck('name')->toArray();
        $schoolModules = array_filter($permissions, function($permission) {
            return str_contains($permission, 'student') ||
                   str_contains($permission, 'attendance') ||
                   str_contains($permission, 'exam') ||
                   str_contains($permission, 'academic') ||
                   str_contains($permission, 'communication') ||
                   str_contains($permission, 'report');
        });

        if (!empty($schoolModules)) {
            $enhancement .= "\nAvailable School Management Modules: " . implode(', ', array_slice($schoolModules, 0, 10));
        }

        $enhancement .= "\n\nI can help you with school management tasks. Please be specific about what you need.";

        return $message . $enhancement;
    }

    /**
     * Parse user message for application-specific actions
     */
    private function parseApplicationActions(string $message): array
    {
        $actions = [];
        $messageLower = strtolower($message);

        // Attendance-related intents
        if (preg_match('/mark attendance|take attendance|attendance for/i', $message)) {
            $actions[] = [
                'type' => 'mark_attendance',
                'description' => 'Mark attendance for students',
                'suggested' => true,
                'parameters' => $this->extractAttendanceParameters($message),
            ];
        }

        // Student information intents
        if (preg_match('/student info|student details|find student|search student/i', $message)) {
            $actions[] = [
                'type' => 'get_student_info',
                'description' => 'Get student information',
                'suggested' => true,
                'parameters' => $this->extractStudentParameters($message),
            ];
        }

        // Attendance summary intents
        if (preg_match('/how many.*present|attendance summary|who.*present|attendance today/i', $message)) {
            $actions[] = [
                'type' => 'get_attendance_summary',
                'description' => 'Get attendance summary',
                'suggested' => true,
                'parameters' => ['date' => now()->format('Y-m-d')],
            ];
        }

        // Class list intents
        if (preg_match('/list.*class|show.*class|all classes|available classes/i', $message)) {
            $actions[] = [
                'type' => 'get_class_list',
                'description' => 'Get list of classes',
                'suggested' => true,
                'parameters' => [],
            ];
        }

        // Report generation intents
        if (preg_match('/generate.*report|create.*report|report for/i', $message)) {
            $actions[] = [
                'type' => 'generate_report',
                'description' => 'Generate report',
                'suggested' => true,
                'parameters' => $this->extractReportParameters($message),
            ];
        }

        // Exam/Score recording intents
        if (preg_match('/record.*score|enter.*marks|exam.*score|mark.*exam/i', $message)) {
            $actions[] = [
                'type' => 'record_score',
                'description' => 'Record exam scores',
                'suggested' => true,
                'parameters' => $this->extractScoreParameters($message),
            ];
        }

        // Announcement creation intents
        if (preg_match('/create.*announcement|send.*announcement|announce|announcement.*about/i', $message)) {
            $actions[] = [
                'type' => 'create_announcement',
                'description' => 'Create announcement',
                'suggested' => true,
                'parameters' => $this->extractAnnouncementParameters($message),
            ];
        }

        // Navigation/Help intents
        if (preg_match('/how.*do.*i|how.*to|where.*can.*i|help.*with|guide.*me/i', $message)) {
            $taskType = $this->identifyTaskFromHelpRequest($message);
            if ($taskType) {
                $actions[] = [
                    'type' => $taskType,
                    'description' => 'Get navigation guidance',
                    'suggested' => true,
                    'parameters' => [],
                ];
            }
        }

        return $actions;
    }

    private function parseActionsFromResponse(string $response): array
    {
        $actions = [];

        // Look for explicit action patterns in the response
        if (preg_match('/\[ACTION:([^\]]+)\]/', $response, $matches)) {
            $actionString = $matches[1];
            $actionParts = explode('|', $actionString);

            $actions[] = [
                'type' => $actionParts[0] ?? 'unknown',
                'description' => $actionParts[1] ?? 'No description',
                'parameters' => isset($actionParts[2]) ? json_decode($actionParts[2], true) : [],
            ];
        }

        // Also parse the original user message for common school management intents
        $originalMessage = request()->input('message', '');
        $messageLower = strtolower($originalMessage);

        // Attendance-related intents
        if (preg_match('/mark attendance|take attendance|attendance for/i', $originalMessage)) {
            $actions[] = [
                'type' => 'mark_attendance',
                'description' => 'Mark attendance for students',
                'suggested' => true,
                'parameters' => $this->extractAttendanceParameters($originalMessage),
            ];
        }

        // Student information intents
        if (preg_match('/student info|student details|find student|search student/i', $originalMessage)) {
            $actions[] = [
                'type' => 'get_student_info',
                'description' => 'Get student information',
                'suggested' => true,
                'parameters' => $this->extractStudentParameters($originalMessage),
            ];
        }

        // Attendance summary intents
        if (preg_match('/how many.*present|attendance summary|who.*present|attendance today/i', $originalMessage)) {
            $actions[] = [
                'type' => 'get_attendance_summary',
                'description' => 'Get attendance summary',
                'suggested' => true,
                'parameters' => ['date' => now()->format('Y-m-d')],
            ];
        }

        // Class list intents
        if (preg_match('/list.*class|show.*class|all classes|available classes/i', $originalMessage)) {
            $actions[] = [
                'type' => 'get_class_list',
                'description' => 'Get list of classes',
                'suggested' => true,
                'parameters' => [],
            ];
        }

        // Report generation intents
        if (preg_match('/generate.*report|create.*report|report for/i', $originalMessage)) {
            $actions[] = [
                'type' => 'generate_report',
                'description' => 'Generate report',
                'suggested' => true,
                'parameters' => $this->extractReportParameters($originalMessage),
            ];
        }

        return $actions;
    }

    private function extractAttendanceParameters(string $message): array
    {
        $parameters = [];

        // Extract class/batch names
        if (preg_match('/class\s+([A-Za-z0-9\s]+)/i', $message, $matches)) {
            $parameters['class_name'] = trim($matches[1]);
        }

        // Extract date
        if (preg_match('/(\d{4}-\d{2}-\d{2})/', $message, $matches)) {
            $parameters['date'] = $matches[1];
        } else {
            $parameters['date'] = now()->format('Y-m-d');
        }

        return $parameters;
    }

    private function extractStudentParameters(string $message): array
    {
        $parameters = [];

        // Extract student name or ID
        if (preg_match('/student\s+([A-Za-z\s]+)/i', $message, $matches)) {
            $parameters['search'] = trim($matches[1]);
        }

        return $parameters;
    }

    private function extractReportParameters(string $message): array
    {
        $parameters = [];

        // Extract report type
        if (preg_match('/attendance.*report/i', $message)) {
            $parameters['type'] = 'attendance';
        } elseif (preg_match('/academic.*report/i', $message)) {
            $parameters['type'] = 'academic';
        } elseif (preg_match('/student.*report/i', $message)) {
            $parameters['type'] = 'student';
        }

        // Extract date range
        if (preg_match('/(\d{4}-\d{2}-\d{2})/', $message, $matches)) {
            $parameters['date_from'] = $matches[1];
            $parameters['date_to'] = $matches[1];
        }

        return $parameters;
    }

    private function extractScoreParameters(string $message): array
    {
        $parameters = [];

        // Extract student name or ID
        if (preg_match('/student\s+([A-Za-z\s]+)/i', $message, $matches)) {
            $parameters['student_name'] = trim($matches[1]);
        }

        // Extract score
        if (preg_match('/score\s+(\d+)/i', $message, $matches)) {
            $parameters['score'] = (int)$matches[1];
        }

        // Extract subject
        if (preg_match('/subject\s+([A-Za-z\s]+)/i', $message, $matches)) {
            $parameters['subject'] = trim($matches[1]);
        }

        return $parameters;
    }

    private function extractAnnouncementParameters(string $message): array
    {
        $parameters = [];

        // Extract announcement content/topic
        if (preg_match('/announcement.*about\s+(.+)/i', $message, $matches)) {
            $parameters['title'] = trim($matches[1]);
            $parameters['content'] = "Announcement about " . trim($matches[1]);
        } elseif (preg_match('/announce\s+(.+)/i', $message, $matches)) {
            $parameters['title'] = trim($matches[1]);
            $parameters['content'] = trim($matches[1]);
        }

        return $parameters;
    }

    private function identifyTaskFromHelpRequest(string $message): ?string
    {
        $messageLower = strtolower($message);

        if (str_contains($messageLower, 'attendance')) {
            return 'mark_attendance';
        }
        if (str_contains($messageLower, 'score') || str_contains($messageLower, 'mark') || str_contains($messageLower, 'exam')) {
            return 'record_score';
        }
        if (str_contains($messageLower, 'announcement') || str_contains($messageLower, 'announce')) {
            return 'create_announcement';
        }
        if (str_contains($messageLower, 'report')) {
            return 'generate_report';
        }
        if (str_contains($messageLower, 'student')) {
            return 'get_student_info';
        }
        if (str_contains($messageLower, 'class')) {
            return 'get_class_list';
        }

        return null;
    }

    private function executeSpecificAction(string $action, array $parameters): array
    {
        switch ($action) {
            case 'mark_attendance':
                return $this->markAttendance($parameters);

            case 'record_score':
                return $this->recordScore($parameters);

            case 'create_announcement':
                return $this->createAnnouncement($parameters);

            case 'generate_report':
                return $this->generateReport($parameters);

            case 'get_student_info':
                return $this->getStudentInfo($parameters);

            case 'get_attendance_summary':
                return $this->getAttendanceSummary($parameters);

            case 'get_class_list':
                return $this->getClassList($parameters);

            case 'send_notification':
                return $this->sendNotification($parameters);

            default:
                throw new \Exception("Unknown action: {$action}");
        }
    }

    private function markAttendance(array $parameters): array
    {
        try {
            $date = $parameters['date'] ?? now()->format('Y-m-d');
            $batchUuid = $parameters['batch_uuid'] ?? null;
            $studentUuids = $parameters['student_uuids'] ?? [];
            $status = $parameters['status'] ?? 'present';

            if (!$batchUuid && empty($studentUuids)) {
                return [
                    'action' => 'mark_attendance',
                    'status' => 'error',
                    'message' => 'Please specify either a class (batch) or specific students.',
                    'required_parameters' => ['batch_uuid OR student_uuids', 'date (optional)', 'status (optional)'],
                ];
            }

            // Check if attendance module exists
            if (!class_exists('\App\Models\Student\Attendance')) {
                return [
                    'action' => 'mark_attendance',
                    'status' => 'unavailable',
                    'message' => 'Attendance module is not available in this system.',
                ];
            }

            $results = [];

            if ($batchUuid) {
                // Mark attendance for entire class
                $batch = \App\Models\Academic\Batch::where('uuid', $batchUuid)->byTeam()->first();
                if (!$batch) {
                    return [
                        'action' => 'mark_attendance',
                        'status' => 'error',
                        'message' => 'Class not found.',
                    ];
                }

                $students = \App\Models\Student\Student::whereHas('batches', function($q) use ($batch) {
                    $q->where('batch_id', $batch->id);
                })->byTeam()->get();

                foreach ($students as $student) {
                    // This would integrate with actual attendance marking logic
                    $results[] = [
                        'student' => $student->name,
                        'status' => $status,
                        'date' => $date,
                    ];
                }

                return [
                    'action' => 'mark_attendance',
                    'status' => 'success',
                    'message' => "Attendance marked for {$batch->name} on {$date}",
                    'results' => $results,
                    'batch' => $batch->name,
                    'date' => $date,
                    'total_students' => count($results),
                ];
            }

            $guidance = $this->markAttendanceGuidance($parameters);

            // Create a helpful response message
            $className = $parameters['class_name'] ?? 'your class';
            $date = $parameters['date'] ?? 'today';

            $message = "I cannot directly mark attendance yet, but I can guide you through the process.\n\n";
            $message .= "**To mark attendance for {$className} on {$date}:**\n\n";
            $message .= "📍 **Navigation:** {$guidance['menu_path']}\n\n";
            $message .= "**Step-by-step process:**\n";

            foreach ($guidance['steps'] as $step) {
                $message .= "{$step['step']}. **{$step['title']}** - {$step['description']}\n";
            }

            $message .= "\n💡 **Tips:**\n";
            foreach ($guidance['tips'] as $tip) {
                $message .= "• {$tip}\n";
            }

            if (!empty($guidance['alternative_routes'])) {
                $message .= "\n🔄 **Alternative routes:**\n";
                foreach ($guidance['alternative_routes'] as $route) {
                    $message .= "• {$route['name']}: {$route['path']}\n";
                }
            }

            return [
                'action' => 'mark_attendance',
                'status' => 'guidance_provided',
                'message' => $message,
                'parameters' => $parameters,
                'navigation_guidance' => $guidance,
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'mark_attendance',
                'status' => 'error',
                'message' => 'Error marking attendance: ' . $e->getMessage(),
            ];
        }
    }

    private function recordScore(array $parameters): array
    {
        try {
            $studentUuid = $parameters['student_uuid'] ?? null;
            $examUuid = $parameters['exam_uuid'] ?? null;
            $subjectUuid = $parameters['subject_uuid'] ?? null;
            $score = $parameters['score'] ?? null;
            $maxScore = $parameters['max_score'] ?? 100;

            if (!$studentUuid || !$score) {
                return [
                    'action' => 'record_score',
                    'status' => 'error',
                    'message' => 'Please specify student and score.',
                    'required_parameters' => ['student_uuid', 'score', 'exam_uuid (optional)', 'subject_uuid (optional)', 'max_score (optional)'],
                ];
            }

            // Check if exam module exists
            if (!class_exists('\App\Models\Exam\ExamRecord')) {
                return [
                    'action' => 'record_score',
                    'status' => 'unavailable',
                    'message' => 'Exam module is not available in this system.',
                ];
            }

            $student = \App\Models\Student\Student::where('uuid', $studentUuid)->byTeam()->first();
            if (!$student) {
                return [
                    'action' => 'record_score',
                    'status' => 'error',
                    'message' => 'Student not found.',
                ];
            }

            $guidance = $this->recordScoreGuidance($parameters);

            // Create a helpful response message
            $studentName = $student->name ?? 'the student';
            $scoreText = $score ? "score of {$score}" : 'exam scores';

            $message = "I cannot directly record scores yet, but I can guide you through the process.\n\n";
            $message .= "**To record {$scoreText} for {$studentName}:**\n\n";
            $message .= "📍 **Navigation:** {$guidance['menu_path']}\n\n";
            $message .= "**Step-by-step process:**\n";

            foreach ($guidance['steps'] as $step) {
                $message .= "{$step['step']}. **{$step['title']}** - {$step['description']}\n";
            }

            $message .= "\n💡 **Tips:**\n";
            foreach ($guidance['tips'] as $tip) {
                $message .= "• {$tip}\n";
            }

            if (!empty($guidance['alternative_routes'])) {
                $message .= "\n🔄 **Alternative routes:**\n";
                foreach ($guidance['alternative_routes'] as $route) {
                    $message .= "• {$route['name']}: {$route['path']}\n";
                }
            }

            return [
                'action' => 'record_score',
                'status' => 'guidance_provided',
                'message' => $message,
                'student' => $student->name,
                'score' => $score,
                'max_score' => $maxScore,
                'percentage' => $score && $maxScore ? round(($score / $maxScore) * 100, 2) : null,
                'navigation_guidance' => $guidance,
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'record_score',
                'status' => 'error',
                'message' => 'Error recording score: ' . $e->getMessage(),
            ];
        }
    }

    private function createAnnouncement(array $parameters): array
    {
        try {
            $title = $parameters['title'] ?? null;
            $content = $parameters['content'] ?? null;
            $audience = $parameters['audience'] ?? ['all'];

            if (!$title || !$content) {
                return [
                    'action' => 'create_announcement',
                    'status' => 'error',
                    'message' => 'Please specify announcement title and content.',
                    'required_parameters' => ['title', 'content', 'audience (optional)'],
                ];
            }

            // Check if communication module exists
            if (!class_exists('\App\Models\Communication\Announcement')) {
                return [
                    'action' => 'create_announcement',
                    'status' => 'unavailable',
                    'message' => 'Communication module is not available in this system.',
                ];
            }

            $guidance = $this->createAnnouncementGuidance($parameters);

            // Create a helpful response message
            $announcementTopic = $title ?: 'your announcement';

            $message = "I cannot directly create announcements yet, but I can guide you through the process.\n\n";
            $message .= "**To create an announcement about '{$announcementTopic}':**\n\n";
            $message .= "📍 **Navigation:** {$guidance['menu_path']}\n\n";
            $message .= "**Step-by-step process:**\n";

            foreach ($guidance['steps'] as $step) {
                $message .= "{$step['step']}. **{$step['title']}** - {$step['description']}\n";
            }

            $message .= "\n💡 **Tips:**\n";
            foreach ($guidance['tips'] as $tip) {
                $message .= "• {$tip}\n";
            }

            if (!empty($guidance['alternative_routes'])) {
                $message .= "\n🔄 **Alternative communication methods:**\n";
                foreach ($guidance['alternative_routes'] as $route) {
                    $message .= "• {$route['name']}: {$route['path']}\n";
                }
            }

            return [
                'action' => 'create_announcement',
                'status' => 'guidance_provided',
                'message' => $message,
                'title' => $title,
                'content' => $content ? substr($content, 0, 100) . '...' : null,
                'audience' => $audience,
                'navigation_guidance' => $guidance,
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'create_announcement',
                'status' => 'error',
                'message' => 'Error creating announcement: ' . $e->getMessage(),
            ];
        }
    }

    private function generateReport(array $parameters): array
    {
        try {
            $reportType = $parameters['type'] ?? 'attendance';
            $dateFrom = $parameters['date_from'] ?? now()->startOfMonth()->format('Y-m-d');
            $dateTo = $parameters['date_to'] ?? now()->format('Y-m-d');
            $batchUuid = $parameters['batch_uuid'] ?? null;

            switch ($reportType) {
                case 'attendance':
                    return $this->generateAttendanceReport($dateFrom, $dateTo, $batchUuid);
                case 'academic':
                    return $this->generateAcademicReport($dateFrom, $dateTo, $batchUuid);
                case 'student':
                    $studentUuid = $parameters['student_uuid'] ?? null;
                    return $this->generateStudentReport($studentUuid);
                default:
                    return [
                        'action' => 'generate_report',
                        'status' => 'error',
                        'message' => 'Unknown report type. Available types: attendance, academic, student',
                        'available_types' => ['attendance', 'academic', 'student'],
                    ];
            }

        } catch (\Exception $e) {
            return [
                'action' => 'generate_report',
                'status' => 'error',
                'message' => 'Error generating report: ' . $e->getMessage(),
            ];
        }
    }

    private function getStudentInfo(array $parameters): array
    {
        try {
            $studentUuid = $parameters['student_uuid'] ?? null;
            $searchTerm = $parameters['search'] ?? null;

            if (!$studentUuid && !$searchTerm) {
                return [
                    'action' => 'get_student_info',
                    'status' => 'error',
                    'message' => 'Please specify student UUID or search term.',
                    'required_parameters' => ['student_uuid OR search'],
                ];
            }

            if (!class_exists('\App\Models\Student\Student')) {
                return [
                    'action' => 'get_student_info',
                    'status' => 'unavailable',
                    'message' => 'Student module is not available in this system.',
                ];
            }

            $query = \App\Models\Student\Student::query()->byTeam();

            if ($studentUuid) {
                $student = $query->where('uuid', $studentUuid)->first();
            } else {
                $student = $query->where(function($q) use ($searchTerm) {
                    $q->whereHas('contact', function($q) use ($searchTerm) {
                        $q->where('first_name', 'like', "%{$searchTerm}%")
                          ->orWhere('last_name', 'like', "%{$searchTerm}%");
                    })->orWhere('code_number', 'like', "%{$searchTerm}%");
                })->first();
            }

            if (!$student) {
                return [
                    'action' => 'get_student_info',
                    'status' => 'not_found',
                    'message' => 'Student not found.',
                ];
            }

            return [
                'action' => 'get_student_info',
                'status' => 'success',
                'student' => [
                    'uuid' => $student->uuid,
                    'name' => $student->name ?? 'N/A',
                    'code_number' => $student->code_number ?? 'N/A',
                    'email' => $student->contact->email ?? 'N/A',
                    'phone' => $student->contact->contact_number ?? 'N/A',
                ],
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'get_student_info',
                'status' => 'error',
                'message' => 'Error retrieving student info: ' . $e->getMessage(),
            ];
        }
    }

    private function getAttendanceSummary(array $parameters): array
    {
        try {
            $date = $parameters['date'] ?? now()->format('Y-m-d');
            $batchUuid = $parameters['batch_uuid'] ?? null;

            if (!class_exists('\App\Models\Student\Attendance')) {
                return [
                    'action' => 'get_attendance_summary',
                    'status' => 'unavailable',
                    'message' => 'Attendance module is not available in this system.',
                ];
            }

            // This would integrate with actual attendance service
            return [
                'action' => 'get_attendance_summary',
                'status' => 'partial',
                'message' => 'Attendance summary functionality needs full implementation with attendance service.',
                'date' => $date,
                'batch_uuid' => $batchUuid,
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'get_attendance_summary',
                'status' => 'error',
                'message' => 'Error getting attendance summary: ' . $e->getMessage(),
            ];
        }
    }

    private function getClassList(array $parameters): array
    {
        try {
            if (!class_exists('\App\Models\Academic\Batch')) {
                return [
                    'action' => 'get_class_list',
                    'status' => 'unavailable',
                    'message' => 'Academic module is not available in this system.',
                ];
            }

            $batches = \App\Models\Academic\Batch::query()
                ->byTeam()
                ->with(['course'])
                ->get()
                ->map(function($batch) {
                    return [
                        'uuid' => $batch->uuid,
                        'name' => $batch->name,
                        'course' => $batch->course->name ?? 'N/A',
                        'full_name' => ($batch->course->name ?? '') . ' - ' . $batch->name,
                    ];
                });

            return [
                'action' => 'get_class_list',
                'status' => 'success',
                'classes' => $batches->toArray(),
                'total' => $batches->count(),
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'get_class_list',
                'status' => 'error',
                'message' => 'Error getting class list: ' . $e->getMessage(),
            ];
        }
    }

    private function sendNotification(array $parameters): array
    {
        try {
            $message = $parameters['message'] ?? null;
            $recipients = $parameters['recipients'] ?? [];
            $type = $parameters['type'] ?? 'general';

            if (!$message) {
                return [
                    'action' => 'send_notification',
                    'status' => 'error',
                    'message' => 'Please specify notification message.',
                    'required_parameters' => ['message', 'recipients (optional)', 'type (optional)'],
                ];
            }

            return [
                'action' => 'send_notification',
                'status' => 'partial',
                'message' => 'Notification sending functionality needs full implementation with communication service.',
                'notification_message' => substr($message, 0, 100) . '...',
                'recipients' => $recipients,
                'type' => $type,
            ];

        } catch (\Exception $e) {
            return [
                'action' => 'send_notification',
                'status' => 'error',
                'message' => 'Error sending notification: ' . $e->getMessage(),
            ];
        }
    }

    private function generateAttendanceReport(string $dateFrom, string $dateTo, ?string $batchUuid): array
    {
        return [
            'action' => 'generate_report',
            'type' => 'attendance',
            'status' => 'partial',
            'message' => 'Attendance report generation needs full implementation.',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'batch_uuid' => $batchUuid,
        ];
    }

    private function generateAcademicReport(string $dateFrom, string $dateTo, ?string $batchUuid): array
    {
        return [
            'action' => 'generate_report',
            'type' => 'academic',
            'status' => 'partial',
            'message' => 'Academic report generation needs full implementation.',
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'batch_uuid' => $batchUuid,
        ];
    }

    private function generateStudentReport(?string $studentUuid): array
    {
        if (!$studentUuid) {
            return [
                'action' => 'generate_report',
                'type' => 'student',
                'status' => 'error',
                'message' => 'Student UUID is required for student report.',
            ];
        }

        return [
            'action' => 'generate_report',
            'type' => 'student',
            'status' => 'partial',
            'message' => 'Student report generation needs full implementation.',
            'student_uuid' => $studentUuid,
        ];
    }

    /**
     * Get navigation guidance for specific school management tasks
     */
    public function getNavigationGuidance(string $task, array $parameters = []): array
    {
        $guidance = $this->getTaskGuidance($task, $parameters);

        if (!$guidance) {
            return [
                'action' => 'navigation_guidance',
                'status' => 'not_found',
                'message' => 'I don\'t have specific guidance for this task yet.',
                'suggestion' => 'Please check the main navigation menu or contact your system administrator.',
            ];
        }

        return [
            'action' => 'navigation_guidance',
            'status' => 'success',
            'task' => $task,
            'guidance' => $guidance,
        ];
    }

    /**
     * Get detailed step-by-step guidance for specific tasks
     */
    private function getTaskGuidance(string $task, array $parameters = []): ?array
    {
        $taskMap = [
            'mark_attendance' => 'markAttendanceGuidance',
            'record_score' => 'recordScoreGuidance',
            'create_announcement' => 'createAnnouncementGuidance',
            'generate_report' => 'generateReportGuidance',
            'manage_students' => 'manageStudentsGuidance',
            'view_student_info' => 'viewStudentInfoGuidance',
            'manage_exams' => 'manageExamsGuidance',
            'view_attendance_summary' => 'viewAttendanceSummaryGuidance',
            'manage_academic_settings' => 'manageAcademicSettingsGuidance',
            'send_communication' => 'sendCommunicationGuidance',
        ];

        $method = $taskMap[$task] ?? null;

        if ($method && method_exists($this, $method)) {
            return $this->$method($parameters);
        }

        return null;
    }

    private function markAttendanceGuidance(array $parameters = []): array
    {
        return [
            'title' => 'How to Mark Attendance',
            'description' => 'Follow these steps to mark attendance for students in your school.',
            'menu_path' => 'Student → Attendance → Student Attendance',
            'route_name' => 'StudentAttendanceList',
            'steps' => [
                [
                    'step' => 1,
                    'title' => 'Navigate to Student Attendance',
                    'description' => 'From the main menu, click on "Student" then "Attendance" then "Student Attendance"',
                    'menu_clicks' => ['Student', 'Attendance', 'Student Attendance'],
                    'icon' => 'fas fa-user-graduate',
                ],
                [
                    'step' => 2,
                    'title' => 'Select Date and Class',
                    'description' => 'Choose the date and class/batch for which you want to mark attendance',
                    'details' => 'Use the date picker and class dropdown to select your target',
                ],
                [
                    'step' => 3,
                    'title' => 'Mark Individual Students',
                    'description' => 'Mark each student as Present (P), Absent (A), Late (L), or Holiday (H)',
                    'details' => 'Click on each student\'s status to change it',
                ],
                [
                    'step' => 4,
                    'title' => 'Save Attendance',
                    'description' => 'Click "Save" or "Submit" to record the attendance',
                    'note' => 'Make sure to save your changes before leaving the page',
                ],
            ],
            'alternative_routes' => [
                [
                    'name' => 'Employee Attendance (for staff)',
                    'path' => 'Employee → Attendance → Employee Attendance',
                    'route_name' => 'EmployeeAttendanceList',
                ],
                [
                    'name' => 'Quick Attendance Assistant',
                    'path' => 'Direct link to Attendance Assistant',
                    'route_name' => 'AttendanceAssistant',
                ],
            ],
            'permissions_required' => ['student:list-attendance'],
            'tips' => [
                'You can mark attendance for multiple days at once',
                'Use bulk actions to mark all students present/absent quickly',
                'Check the attendance summary to verify your entries',
            ],
        ];
    }

    private function recordScoreGuidance(array $parameters = []): array
    {
        return [
            'title' => 'How to Record Exam Scores',
            'description' => 'Follow these steps to record student exam scores and marks.',
            'menu_path' => 'Exam → Mark',
            'route_name' => 'ExamMark',
            'steps' => [
                [
                    'step' => 1,
                    'title' => 'Navigate to Exam Marks',
                    'description' => 'From the main menu, click on "Exam" then "Mark"',
                    'menu_clicks' => ['Exam', 'Mark'],
                    'icon' => 'fas fa-newspaper',
                ],
                [
                    'step' => 2,
                    'title' => 'Select Exam and Subject',
                    'description' => 'Choose the exam, class/batch, and subject for score entry',
                    'details' => 'Use the dropdown filters to narrow down to your specific exam',
                ],
                [
                    'step' => 3,
                    'title' => 'Enter Student Scores',
                    'description' => 'Enter marks for each student in the provided fields',
                    'details' => 'You can enter marks for different assessment types (written, practical, etc.)',
                ],
                [
                    'step' => 4,
                    'title' => 'Review and Submit',
                    'description' => 'Review all entered scores and click "Submit" to save',
                    'note' => 'Double-check all scores before submitting as changes may require approval',
                ],
            ],
            'alternative_routes' => [
                [
                    'name' => 'Exam Schedule (to view exam dates)',
                    'path' => 'Exam → Schedule',
                    'route_name' => 'ExamSchedule',
                ],
                [
                    'name' => 'Import Marks (bulk upload)',
                    'path' => 'Exam → Mark → Import',
                    'route_name' => 'ExamMarkImport',
                ],
            ],
            'permissions_required' => ['exam:marks-record'],
            'tips' => [
                'You can import marks from Excel files for bulk entry',
                'Use the assessment breakdown for detailed scoring',
                'Check exam schedules to ensure you\'re entering marks for the correct exam',
            ],
        ];
    }

    private function createAnnouncementGuidance(array $parameters = []): array
    {
        return [
            'title' => 'How to Create Announcements',
            'description' => 'Follow these steps to create and send announcements to students, parents, or staff.',
            'menu_path' => 'Communication → Announcements',
            'route_name' => 'CommunicationAnnouncementList',
            'steps' => [
                [
                    'step' => 1,
                    'title' => 'Navigate to Announcements',
                    'description' => 'From the main menu, click on "Communication" then "Announcements"',
                    'menu_clicks' => ['Communication', 'Announcements'],
                    'icon' => 'fas fa-bullhorn',
                ],
                [
                    'step' => 2,
                    'title' => 'Create New Announcement',
                    'description' => 'Click the "Create" or "New Announcement" button',
                    'details' => 'Look for the + icon or "Create" button in the top section',
                ],
                [
                    'step' => 3,
                    'title' => 'Fill Announcement Details',
                    'description' => 'Enter title, content, and select announcement type',
                    'details' => 'Choose appropriate announcement type (General, Academic, Event, etc.)',
                ],
                [
                    'step' => 4,
                    'title' => 'Select Audience',
                    'description' => 'Choose who should receive the announcement (Students, Parents, Staff)',
                    'details' => 'You can select specific classes, departments, or all users',
                ],
                [
                    'step' => 5,
                    'title' => 'Schedule and Publish',
                    'description' => 'Set publication date and click "Publish" or "Send"',
                    'note' => 'You can schedule announcements for future dates',
                ],
            ],
            'alternative_routes' => [
                [
                    'name' => 'Email Communication',
                    'path' => 'Communication → Email',
                    'route_name' => 'CommunicationEmail',
                ],
                [
                    'name' => 'SMS Communication',
                    'path' => 'Communication → SMS',
                    'route_name' => 'CommunicationSMS',
                ],
            ],
            'permissions_required' => ['announcement:read', 'announcement:create'],
            'tips' => [
                'Use announcement types to categorize your messages',
                'Preview your announcement before publishing',
                'Check the audience selection to ensure proper targeting',
            ],
        ];
    }

    private function manageStudentsGuidance(array $parameters = []): array
    {
        return [
            'title' => 'How to Manage Students',
            'description' => 'Follow these steps to view, add, edit, and manage student information.',
            'menu_path' => 'Students → Students',
            'route_name' => 'StudentList',
            'steps' => [
                [
                    'step' => 1,
                    'title' => 'Navigate to Students',
                    'description' => 'From the main menu, click on "Students" then "Students"',
                    'menu_clicks' => ['Students', 'Students'],
                    'icon' => 'fas fa-user-graduate',
                ],
                [
                    'step' => 2,
                    'title' => 'View Student List',
                    'description' => 'Browse through the list of students or use search/filters',
                    'details' => 'Use the search bar to find specific students by name or ID',
                ],
                [
                    'step' => 3,
                    'title' => 'Add New Student (if needed)',
                    'description' => 'Click "Create" or "Add Student" to register a new student',
                    'details' => 'Fill in all required information including personal details and academic info',
                ],
                [
                    'step' => 4,
                    'title' => 'View/Edit Student Details',
                    'description' => 'Click on a student name to view their profile and edit information',
                    'details' => 'You can update contact info, academic records, and other details',
                ],
            ],
            'alternative_routes' => [
                [
                    'name' => 'Student Registration',
                    'path' => 'Student → Registration',
                    'route_name' => 'StudentRegistration',
                ],
                [
                    'name' => 'Student Reports',
                    'path' => 'Student → Reports',
                    'route_name' => 'StudentReport',
                ],
            ],
            'permissions_required' => ['student:read', 'student:create', 'student:edit'],
            'tips' => [
                'Use filters to find students by class, status, or other criteria',
                'Export student lists for external use',
                'Check student profiles for complete information',
            ],
        ];
    }
}
